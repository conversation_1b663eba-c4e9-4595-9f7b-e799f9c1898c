<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.teacher.mapper.TcCourseStageMapper">

    <resultMap type="com.ruoyi.project.teacher.dto.TcCourseStageDTO" id="TcCourseStageResult">
        <result property="courseStageId"    column="course_stage_id"    />
        <result property="courseId"         column="course_id"          />
        <result property="courseStageTitle" column="course_stage_title" />
        <result property="courseStageText"  column="course_stage_text"  />
        <result property="courseStageOrder" column="course_stage_order" />
        <result property="createTime"       column="create_time"        />
        <result property="modifyTime"       column="modify_time"        />
        <result property="isCompleted"      column="is_completed"       />
        <result property="stageStatus"      column="stage_status"       />
    </resultMap>

    <resultMap type="com.ruoyi.project.teacher.dto.TcCourseStageDTO" id="TcCourseStageWithAnnexResult" extends="TcCourseStageResult">
        <collection property="stageAnnexList" ofType="com.ruoyi.project.teacher.dto.TcCourseStageAnnexDTO">
            <result property="annexId"    column="annex_id"    />
            <result property="stageId"    column="stage_id"    />
            <result property="annexName"  column="annex_name"  />
            <result property="annexPath"  column="annex_path"  />
            <result property="createTime" column="annex_create_time" />
            <result property="modifyTime" column="annex_modify_time" />
        </collection>
    </resultMap>

    <select id="selectCourseStageListByCourseCode" parameterType="String" resultMap="TcCourseStageWithAnnexResult">
        SELECT 
            cs.COURSE_STAGE_ID as course_stage_id,
            cs.COURSE_ID as course_id,
            cs.COURSE_STAGE_TITLE as course_stage_title,
            cs.COURSE_STAGE_TEXT as course_stage_text,
            cs.COURSE_STAGE_ORDER as course_stage_order,
            cs.CREATE_TIME as create_time,
            cs.MODIFY_TIME as modify_time,
            cs.IS_COMPLETED as is_completed,
            CASE 
                WHEN cs.IS_COMPLETED = 1 THEN 'completed'
                ELSE 'pending'
            END as stage_status,
            csa.ANNEX_ID as annex_id,
            csa.STAGE_ID as stage_id,
            csa.ANNEX_NAME as annex_name,
            csa.ANNEX_PATH as annex_path,
            csa.CREATE_TIME as annex_create_time,
            csa.MODIFY_TIME as annex_modify_time
        FROM DC_COURSE_STAGE cs
        LEFT JOIN DC_COURSE c ON cs.COURSE_ID = c.COURSE_ID
        LEFT JOIN DC_COURSE_STAGE_ANNEX csa ON cs.COURSE_STAGE_ID = csa.STAGE_ID
        WHERE c.COURSE_CODE = #{courseCode}
        ORDER BY cs.COURSE_STAGE_ORDER ASC, csa.CREATE_TIME ASC
    </select>

    <select id="selectCourseStageListByCourseId" parameterType="String" resultMap="TcCourseStageWithAnnexResult">
        SELECT 
            cs.COURSE_STAGE_ID as course_stage_id,
            cs.COURSE_ID as course_id,
            cs.COURSE_STAGE_TITLE as course_stage_title,
            cs.COURSE_STAGE_TEXT as course_stage_text,
            cs.COURSE_STAGE_ORDER as course_stage_order,
            cs.CREATE_TIME as create_time,
            cs.MODIFY_TIME as modify_time,
            cs.IS_COMPLETED as is_completed,
            CASE 
                WHEN cs.IS_COMPLETED = 1 THEN 'completed'
                ELSE 'pending'
            END as stage_status,
            csa.ANNEX_ID as annex_id,
            csa.STAGE_ID as stage_id,
            csa.ANNEX_NAME as annex_name,
            csa.ANNEX_PATH as annex_path,
            csa.CREATE_TIME as annex_create_time,
            csa.MODIFY_TIME as annex_modify_time
        FROM DC_COURSE_STAGE cs
        LEFT JOIN DC_COURSE_STAGE_ANNEX csa ON cs.COURSE_STAGE_ID = csa.STAGE_ID
        WHERE cs.COURSE_ID = #{courseId}
        ORDER BY cs.COURSE_STAGE_ORDER ASC, csa.CREATE_TIME ASC
    </select>

</mapper> 