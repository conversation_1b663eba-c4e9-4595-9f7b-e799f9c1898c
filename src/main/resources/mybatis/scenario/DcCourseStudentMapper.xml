<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.scenario.mapper.DcCourseStudentMapper">

    <select id="getStudentList" resultType="com.ruoyi.project.scenario.domain.DcCourseStudent">
        select t.STUDENT_ID,
               t.STUDENT_CODE,
               t.PUPPET_ID,
               t.IS_GROUP_LEADER,
               t.STUDENT_INDEX,
               t1.STUDENT_NAME,
               t1.STUDENT_SEX
        from DC_COURSE_STUDENT t
                 left join ST_STUDENT_INFO t1
                           on t.STUDENT_CODE = t1.STUDENT_CODE
        where PUPPET_ID =#{puppetId}
        order by t.student_index
    </select>

    <update id="updatePlusIndex">
        update DC_COURSE_STUDENT set student_index = student_index + 1
        where puppet_id = #{puppetId}
        and student_index >= #{studentIndex}
    </update>

    <update id="updateMinusIndex">
        update DC_COURSE_STUDENT set student_index = student_index - 1
        where puppet_id = #{puppetId}
        and student_index > #{studentIndex}
    </update>

    <select id="listByPuppetId" resultType="map">
        select STUDENT_ID "id",
               t1.student_name "label"
        from DC_COURSE_STUDENT t
                 left join st_student_info t1
                           on t.STUDENT_CODE = t1.STUDENT_CODE
        where PUPPET_ID =#{puppetId}
        order by STUDENT_INDEX
    </select>
    
</mapper>