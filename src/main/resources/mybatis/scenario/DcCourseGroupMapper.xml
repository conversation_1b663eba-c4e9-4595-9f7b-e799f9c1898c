<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.scenario.mapper.DcCourseGroupMapper">
    
   <select id="getTeGroupList" resultType="map">
       SELECT
       RESOURCE_ID,
       GROUP_NAME
       FROM
       (
       SELECT
       TCI.RESOURCE_ID ,
       TCI.GROUP_NAME
       FROM
       TE_CLASSGROUP_INFO TCI
       LEFT JOIN DC_COURSE DC
       ON
       TCI.GROUP_CLASSCODE = DC.CLASS_CODE
       WHERE
       DC.COURSE_ID = #{courseId}
       ORDER BY
       TCI.GROUP_ORDER)
       UNION ALL
       SELECT
       '-1' RESOURCE_ID,
       '未分组' GROUP_NAME
       FROM
       DUAL
   </select>

    <select id="getStudentListByGroup" resultType="map">
        select t.RESOURCE_ID,
        t.STUDENT_CODE,
        t.STUDENT_NAME,
        t.STUDENT_SEX
        from ST_STUDENT_INFO t
        where STUDENT_CLASSCODE =#{classCode}
          and nvl(STUDENT_GROUP,'-1') = #{groupId}
          and  not exists (SELECT * FROM (select STUDENT_CODE from dc_course_student dcs
        left join dc_course_puppet dcp on dcp.puppet_id = dcs.puppet_id
        left join dc_course_group dcg on dcp.group_id = dcg.group_id
        left join dc_course dc on dc.course_id = dcg.course_id
        where dc.class_code =#{classCode}) t1 WHERE t1.STUDENT_CODE = t.STUDENT_CODE)

    </select>


    <select id="getListByCourseId" resultType="map">
        select GROUP_ID id, GROUP_NAME label from DC_COURSE_GROUP where course_id = #{courseId} order by group_order
    </select>


</mapper>