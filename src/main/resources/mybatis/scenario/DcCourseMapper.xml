<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.scenario.mapper.DcCourseMapper">

    <select id="selectGetTagsByUser" resultType="map">
        select DISTINCT t2.TAG_ID,
                        t2.TAG_NAME,
                        t2.TAG_INDEX
        from DC_COURSE t
                 left join dc_scene t1
                           on t1.scene_Id = t.SCENE_ID
                 left join DC_SCENE_TAG t2
                           on t2.tag_id = t1.SCENE_TAGID
        where t2.TAG_ID is not null
          and t.CREATE_USER = #{userId}
        order by t2.TAG_INDEX
    </select>

    <select id="selectDcCourseList" resultType="com.ruoyi.project.scenario.domain.DcCourse">
        SELECT T.COURSE_ID ,
               T.COURSE_NAME ,
               T.COURSE_CODE,
               DS.SCENE_NAME SCENE_ID,
               TCI.CLASS_NAME CLASS_CODE,
               DS.SCENE_IMAGE
        FROM DC_COURSE T
                 LEFT JOIN DC_SCENE DS
                           ON DS.SCENE_ID = T.SCENE_ID
                 LEFT JOIN TE_CLASS_INFO TCI
                           ON TCI.CLASS_CODE = T.CLASS_CODE
        WHERE t.ISDELETE !='1'
        and T.CREATE_USER =#{userId}
        and ds.SCENE_TAGID =#{tagId}
    </select>

    <select id="getClassList" resultType="map">
        SELECT CLASS_CODE,
               CLASS_NAME
        FROM TE_CLASS_INFO T
        WHERE T.CLASS_STATE ='1'
          AND T.CLASS_ENDTIME > TO_CHAR(SYSDATE(),'YYYY-MM-DD')
    </select>

    <select id="checkRandomString" resultType="map">
        SELECT count(*)COURSE_CODE_COUNT FROM DC_COURSE  WHERE COURSE_CODE = #{randomString}
    </select>

    <select id="selectDcCourseByCourseId" resultType="com.ruoyi.project.scenario.domain.DcCourse">
        select t.*,
               t1.SCENE_IMAGE
        from DC_COURSE t
                 left join DC_SCENE t1
                           on t.SCENE_ID = t1.SCENE_ID
        where t.course_id = #{courseId}
    </select>

</mapper>