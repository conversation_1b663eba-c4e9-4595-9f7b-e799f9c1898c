package com.ruoyi.project.teacher.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;

/**
 * 教师端监控统计信息VO
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TcMonitorStatisticsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总群组数
     */
    private Integer totalGroups;

    /**
     * 总学生数
     */
    private Integer totalStudents;

    /**
     * 在线学生数
     */
    private Integer onlineStudents;

    /**
     * 今日消息数
     */
    private Long todayMessages;

    /**
     * 活跃群组数
     */
    private Integer activeGroups;

    /**
     * 最后更新时间
     */
    private String lastUpdateTime;
} 