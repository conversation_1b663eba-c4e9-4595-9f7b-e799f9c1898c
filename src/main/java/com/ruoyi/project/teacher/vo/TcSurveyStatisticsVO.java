package com.ruoyi.project.teacher.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 教师端问卷统计结果VO
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TcSurveyStatisticsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 问卷ID
     */
    private String paperId;

    /**
     * 问卷标题
     */
    private String paperTitle;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 总学生数
     */
    private Integer totalStudents;

    /**
     * 已提交数量
     */
    private Integer submittedCount;

    /**
     * 提交率（百分比）
     */
    private Double submissionRate;

    /**
     * 题目统计列表
     */
    private List<QuestionStatistics> questionStatistics;

    /**
     * 题目统计内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class QuestionStatistics implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 题目ID
         */
        private String questionId;

        /**
         * 题目文本
         */
        private String questionText;

        /**
         * 题目类型（1-单选，2-多选）
         */
        private Integer questionType;

        /**
         * 题目序号
         */
        private Integer questionOrder;

        /**
         * 选项统计列表
         */
        private List<OptionStatistics> optionStatistics;

        /**
         * 回答总数
         */
        private Integer totalAnswers;
    }

    /**
     * 选项统计内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class OptionStatistics implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 选项ID
         */
        private String itemId;

        /**
         * 选项文本
         */
        private String itemText;

        /**
         * 选择次数
         */
        private Integer selectCount;

        /**
         * 选择率（百分比）
         */
        private Double selectRate;

        /**
         * 是否正确答案
         */
        private String isRight;
    }
} 