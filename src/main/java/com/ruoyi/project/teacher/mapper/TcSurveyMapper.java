package com.ruoyi.project.teacher.mapper;

import com.ruoyi.project.teacher.dto.TcSurveyListDTO;
import com.ruoyi.project.teacher.dto.TcSurveyQuestionDTO;
import com.ruoyi.project.teacher.dto.TcSurveyOptionDTO;
import com.ruoyi.project.teacher.dto.TcSurveyBasicInfoDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 教师端问卷调查Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Mapper
public interface TcSurveyMapper {

    /**
     * 根据课程代码查询问卷列表（含提交统计）
     * 
     * @param courseCode 课程代码
     * @return 问卷列表
     */
    List<TcSurveyListDTO> selectSurveyListByCourseCode(@Param("courseCode") String courseCode);

    /**
     * 根据问卷ID查询问卷基本信息
     * 
     * @param paperId 问卷ID
     * @return 问卷基本信息
     */
    TcSurveyBasicInfoDTO selectSurveyBasicInfo(@Param("paperId") String paperId);

    /**
     * 根据问卷ID查询问卷题目列表
     * 
     * @param paperId 问卷ID
     * @return 题目列表
     */
    List<TcSurveyQuestionDTO> selectQuestionsByPaperId(@Param("paperId") String paperId);

    /**
     * 根据题目ID查询选项列表
     * 
     * @param questionId 题目ID
     * @return 选项列表
     */
    List<TcSurveyOptionDTO> selectOptionsByQuestionId(@Param("questionId") String questionId);

    /**
     * 根据问卷ID批量查询所有题目的选项列表
     * 
     * @param paperId 问卷ID
     * @return 选项列表（包含questionId用于分组）
     */
    List<TcSurveyOptionDTO> selectAllOptionsByPaperId(@Param("paperId") String paperId);

    /**
     * 根据问卷ID查询总学生数
     * 
     * @param paperId 问卷ID
     * @return 总学生数
     */
    Integer selectTotalStudentsByPaperId(@Param("paperId") String paperId);

    /**
     * 根据问卷ID查询已提交数量
     * 
     * @param paperId 问卷ID
     * @return 已提交数量
     */
    Integer selectSubmittedCountByPaperId(@Param("paperId") String paperId);

    /**
     * 根据题目ID和选项ID查询选择次数
     * 
     * @param questionId 题目ID
     * @param itemId 选项ID
     * @return 选择次数
     */
    Integer selectOptionSelectCount(@Param("questionId") String questionId, @Param("itemId") String itemId);

    /**
     * 根据题目ID查询回答总数
     * 
     * @param questionId 题目ID
     * @return 回答总数
     */
    Integer selectQuestionAnswerCount(@Param("questionId") String questionId);
} 