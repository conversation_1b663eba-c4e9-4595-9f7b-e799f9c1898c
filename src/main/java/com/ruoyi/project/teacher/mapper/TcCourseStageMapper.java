package com.ruoyi.project.teacher.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.scenario.domain.DcCourseStage;
import com.ruoyi.project.teacher.dto.TcCourseStageDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 教师端课程阶段Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Mapper
public interface TcCourseStageMapper extends BaseMapper<DcCourseStage> {

    /**
     * 根据课程代码查询课程阶段列表（包含附件）
     * 
     * @param courseCode 课程代码
     * @return 课程阶段列表
     */
    List<TcCourseStageDTO> selectCourseStageListByCourseCode(@Param("courseCode") String courseCode);

    /**
     * 根据课程ID查询课程阶段列表（包含附件）
     * 
     * @param courseId 课程ID
     * @return 课程阶段列表
     */
    List<TcCourseStageDTO> selectCourseStageListByCourseId(@Param("courseId") String courseId);
} 