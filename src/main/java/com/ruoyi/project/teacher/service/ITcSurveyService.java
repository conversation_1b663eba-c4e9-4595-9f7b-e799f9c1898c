package com.ruoyi.project.teacher.service;

import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.teacher.dto.TcSurveyListDTO;
import com.ruoyi.project.teacher.vo.TcSurveyStatisticsVO;

import java.util.List;

/**
 * 教师端问卷调查Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface ITcSurveyService {

    /**
     * 根据课程代码查询问卷列表
     * 
     * @param courseCode 课程代码
     * @return 问卷列表
     */
    List<TcSurveyListDTO> getSurveyListByCourseCode(String courseCode);

    /**
     * 根据问卷ID查询问卷统计结果
     * 
     * @param paperId 问卷ID
     * @return 问卷统计结果
     */
    TcSurveyStatisticsVO getSurveyStatistics(String paperId);

    /**
     * 调试方法：测试问卷题目查询
     * 
     * @param paperId 问卷ID
     * @return 调试信息
     */
    AjaxResult debugQuestions(String paperId);
} 