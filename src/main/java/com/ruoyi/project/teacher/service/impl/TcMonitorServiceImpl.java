package com.ruoyi.project.teacher.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.project.scenario.domain.DcCourse;
import com.ruoyi.project.scenario.mapper.DcCourseMapper;
import com.ruoyi.project.student.websocket.support.RedisUserConnectionManager;
import com.ruoyi.project.teacher.dto.TcCourseGroupDTO;
import com.ruoyi.project.teacher.service.ITcCourseGroupService;
import com.ruoyi.project.teacher.service.ITcMonitorService;
import com.ruoyi.project.teacher.vo.TcCourseInfoVO;
import com.ruoyi.project.teacher.vo.TcMonitorStatisticsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 教师端监控Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TcMonitorServiceImpl implements ITcMonitorService {

    private final DcCourseMapper dcCourseMapper;
    private final ITcCourseGroupService tcCourseGroupService;
    private final RedisUserConnectionManager redisUserConnectionManager;

    /**
     * 验证课程码是否有效
     * 
     * @param courseCode 课程码
     * @return 是否有效
     */
    @Override
    public boolean validateCourseCode(String courseCode) {
        log.debug("验证课程码: {}", courseCode);
        
        if (!StringUtils.hasText(courseCode)) {
            return false;
        }
        
        try {
            // 查询课程是否存在且状态正常
            LambdaQueryWrapper<DcCourse> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DcCourse::getCourseCode, courseCode)
                       .eq(DcCourse::getIsDelete, 0); // 未删除状态
            
            DcCourse course = dcCourseMapper.selectOne(queryWrapper);
            boolean isValid = course != null;
            
            log.debug("课程码 {} 验证结果: {}", courseCode, isValid);
            return isValid;
        } catch (Exception e) {
            log.error("验证课程码异常: {}", courseCode, e);
            return false;
        }
    }

    /**
     * 获取课程信息
     * 
     * @param courseCode 课程码
     * @return 课程信息
     */
    @Override
    public TcCourseInfoVO getCourseInfo(String courseCode) {
        log.debug("获取课程信息: {}", courseCode);
        
        if (!StringUtils.hasText(courseCode)) {
            return null;
        }
        
        try {
            // 查询课程基本信息
            LambdaQueryWrapper<DcCourse> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DcCourse::getCourseCode, courseCode)
                       .eq(DcCourse::getIsDelete, 0);
            
            DcCourse course = dcCourseMapper.selectOne(queryWrapper);
            if (course == null) {
                return null;
            }
            
            // 获取课程群组信息统计
            List<TcCourseGroupDTO> groups = tcCourseGroupService.getCourseGroupsByCourseCode(courseCode);
            
            // 计算统计数据
            int groupCount = groups.size();
            int totalStudents = groups.stream()
                    .mapToInt(group -> group.getStudentCount() != null ? group.getStudentCount() : 0)
                    .sum();
            
            // 计算在线学生数（从Redis获取）
            int onlineStudents = 0;
            try {
                for (TcCourseGroupDTO group : groups) {
                    onlineStudents += redisUserConnectionManager.getGroupUserCount(group.getGroupId());
                }
            } catch (Exception e) {
                log.warn("获取在线学生数失败", e);
            }
            
            // 构建返回对象
            return TcCourseInfoVO.builder()
                    .courseId(course.getCourseId())
                    .courseCode(course.getClassCode())
                    .courseName(course.getCourseName())
                    .sceneName(course.getSceneId()) // 使用sceneId作为sceneName
                    .status(course.getIsDelete()) // 使用isDelete作为状态
                    .createTime(course.getCreateTime())
                    .groupCount(groupCount)
                    .totalStudents(totalStudents)
                    .onlineStudents(onlineStudents)
                    .build();
        } catch (Exception e) {
            log.error("获取课程信息异常: {}", courseCode, e);
            return null;
        }
    }

    /**
     * 获取监控统计信息
     * 
     * @param courseCode 课程码（可选）
     * @return 统计信息
     */
    @Override
    public TcMonitorStatisticsVO getMonitorStatistics(String courseCode) {
        log.debug("获取监控统计信息，课程码: {}", courseCode);
        
        try {
            List<TcCourseGroupDTO> groups;
            
            if (StringUtils.hasText(courseCode)) {
                // 获取指定课程的群组
                groups = tcCourseGroupService.getCourseGroupsByCourseCode(courseCode);
            } else {
                // 获取所有群组（这里可以根据当前用户的权限进行过滤）
                groups = tcCourseGroupService.getCourseGroupsByDeptId(null);
            }
            
            // 计算统计数据
            int totalGroups = groups.size();
            int totalStudents = groups.stream()
                    .mapToInt(group -> group.getStudentCount() != null ? group.getStudentCount() : 0)
                    .sum();
            
            // 计算在线学生数
            int onlineStudents = 0;
            int activeGroups = 0;
            
            try {
                for (TcCourseGroupDTO group : groups) {
                    int groupOnline = redisUserConnectionManager.getGroupUserCount(group.getGroupId());
                    onlineStudents += groupOnline;
                    if (groupOnline > 0) {
                        activeGroups++;
                    }
                }
            } catch (Exception e) {
                log.warn("获取在线统计数据失败", e);
            }
            
            // 格式化当前时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String lastUpdateTime = sdf.format(new Date());
            
            return TcMonitorStatisticsVO.builder()
                    .totalGroups(totalGroups)
                    .totalStudents(totalStudents)
                    .onlineStudents(onlineStudents)
                    .todayMessages(0L) // 今日消息数暂时设为0，后续可以实现
                    .activeGroups(activeGroups)
                    .lastUpdateTime(lastUpdateTime)
                    .build();
        } catch (Exception e) {
            log.error("获取监控统计信息异常，课程码: {}", courseCode, e);
            throw new RuntimeException("获取统计信息失败", e);
        }
    }
} 