package com.ruoyi.project.teacher.service.impl;

import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.teacher.dto.TcSurveyListDTO;
import com.ruoyi.project.teacher.dto.TcSurveyQuestionDTO;
import com.ruoyi.project.teacher.dto.TcSurveyOptionDTO;
import com.ruoyi.project.teacher.dto.TcSurveyBasicInfoDTO;
import com.ruoyi.project.teacher.mapper.TcSurveyMapper;
import com.ruoyi.project.teacher.service.ITcSurveyService;
import com.ruoyi.project.teacher.vo.TcSurveyStatisticsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 教师端问卷调查Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TcSurveyServiceImpl implements ITcSurveyService {

    private final TcSurveyMapper tcSurveyMapper;

    /**
     * 根据课程代码查询问卷列表
     * 
     * @param courseCode 课程代码
     * @return 问卷列表
     */
    @Override
    public List<TcSurveyListDTO> getSurveyListByCourseCode(String courseCode) {
        log.debug("根据课程代码查询问卷列表，courseCode: {}", courseCode);
        
        if (!StringUtils.hasText(courseCode)) {
            log.warn("课程代码为空，返回空列表");
            return new ArrayList<>();
        }
        
        try {
            List<TcSurveyListDTO> surveyList = tcSurveyMapper.selectSurveyListByCourseCode(courseCode);
            log.debug("查询到课程代码 {} 的问卷数量: {}", courseCode, surveyList.size());
            
            // 计算提交率
            for (TcSurveyListDTO survey : surveyList) {
                if (survey.getTotalStudents() != null && survey.getTotalStudents() > 0 
                    && survey.getSubmittedCount() != null) {
                    double rate = (double) survey.getSubmittedCount() / survey.getTotalStudents() * 100;
                    survey.setSubmissionRate(BigDecimal.valueOf(rate).setScale(2, RoundingMode.HALF_UP).doubleValue());
                } else {
                    survey.setSubmissionRate(0.0);
                }
            }
            
            return surveyList;
        } catch (Exception e) {
            log.error("根据课程代码查询问卷列表异常，courseCode: {}", courseCode, e);
            throw new RuntimeException("查询问卷列表失败", e);
        }
    }

    /**
     * 根据问卷ID查询问卷统计结果
     * 
     * @param paperId 问卷ID
     * @return 问卷统计结果
     */
    @Override
    public TcSurveyStatisticsVO getSurveyStatistics(String paperId) {
        log.debug("根据问卷ID查询问卷统计结果，paperId: {}", paperId);
        
        if (!StringUtils.hasText(paperId)) {
            log.warn("问卷ID为空，返回null");
            return null;
        }
        
        try {
            // 查询问卷基本信息
            TcSurveyBasicInfoDTO basicInfo = tcSurveyMapper.selectSurveyBasicInfo(paperId);
            if (basicInfo == null) {
                log.warn("问卷基本信息不存在，paperId: {}", paperId);
                return null;
            }
            
            // 查询统计数据
            Integer totalStudents = tcSurveyMapper.selectTotalStudentsByPaperId(paperId);
            Integer submittedCount = tcSurveyMapper.selectSubmittedCountByPaperId(paperId);
            
            // 计算提交率
            double submissionRate = 0.0;
            if (totalStudents != null && totalStudents > 0 && submittedCount != null) {
                submissionRate = BigDecimal.valueOf((double) submittedCount / totalStudents * 100)
                        .setScale(2, RoundingMode.HALF_UP).doubleValue();
            }
            
            // 查询题目列表
            List<TcSurveyQuestionDTO> questions = tcSurveyMapper.selectQuestionsByPaperId(paperId);
            
            // 批量查询所有选项，避免N+1查询问题
            List<TcSurveyOptionDTO> allOptions = tcSurveyMapper.selectAllOptionsByPaperId(paperId);
            
            // 按题目ID分组选项，便于后续处理
            Map<String, List<TcSurveyOptionDTO>> optionsMap = allOptions.stream()
                    .collect(Collectors.groupingBy(TcSurveyOptionDTO::getQuestionId));
            
            List<TcSurveyStatisticsVO.QuestionStatistics> questionStatistics = new ArrayList<>();
            
            for (int i = 0; i < questions.size(); i++) {
                TcSurveyQuestionDTO question = questions.get(i);
                String questionId = question.getQuestionId();
                
                // 从分组后的Map中获取选项列表
                List<TcSurveyOptionDTO> options = optionsMap.getOrDefault(questionId, new ArrayList<>());
                List<TcSurveyStatisticsVO.OptionStatistics> optionStatistics = new ArrayList<>();
                
                Integer totalAnswers = tcSurveyMapper.selectQuestionAnswerCount(questionId);
                if (totalAnswers == null) totalAnswers = 0;
                
                for (TcSurveyOptionDTO option : options) {
                    String itemId = option.getItemId();
                    Integer selectCount = tcSurveyMapper.selectOptionSelectCount(questionId, itemId);
                    if (selectCount == null) selectCount = 0;
                    
                    // 计算选择率
                    double selectRate = 0.0;
                    if (totalAnswers > 0) {
                        selectRate = BigDecimal.valueOf((double) selectCount / totalAnswers * 100)
                                .setScale(2, RoundingMode.HALF_UP).doubleValue();
                    }
                    
                    TcSurveyStatisticsVO.OptionStatistics optionStat = TcSurveyStatisticsVO.OptionStatistics.builder()
                            .itemId(itemId)
                            .itemText(option.getItemText())
                            .selectCount(selectCount)
                            .selectRate(selectRate)
                            .isRight(option.getIsRight())
                            .build();
                    
                    optionStatistics.add(optionStat);
                }
                
                TcSurveyStatisticsVO.QuestionStatistics questionStat = TcSurveyStatisticsVO.QuestionStatistics.builder()
                        .questionId(questionId)
                        .questionText(question.getQuestionText())
                        .questionType(question.getQuestionType())
                        .questionOrder(i + 1)
                        .optionStatistics(optionStatistics)
                        .totalAnswers(totalAnswers)
                        .build();
                
                questionStatistics.add(questionStat);
            }
            
            // 构建返回结果
            return TcSurveyStatisticsVO.builder()
                    .paperId(paperId)
                    .paperTitle(basicInfo.getPaperTitle())
                    .sceneName(basicInfo.getSceneName())
                    .createTime(basicInfo.getCreateTime())
                    .totalStudents(totalStudents != null ? totalStudents : 0)
                    .submittedCount(submittedCount != null ? submittedCount : 0)
                    .submissionRate(submissionRate)
                    .questionStatistics(questionStatistics)
                    .build();
                    
        } catch (Exception e) {
            log.error("根据问卷ID查询问卷统计结果异常，paperId: {}", paperId, e);
            throw new RuntimeException("查询问卷统计结果失败", e);
        }
    }

    /**
     * 调试方法：测试问卷题目查询
     * 
     * @param paperId 问卷ID
     * @return 调试信息
     */
    @Override
    public AjaxResult debugQuestions(String paperId) {
        log.info("调试：开始查询问卷题目，paperId: {}", paperId);
        
        Map<String, Object> debugInfo = new HashMap<>();
        
        try {
            // 1. 查询问卷基本信息
            TcSurveyBasicInfoDTO basicInfo = tcSurveyMapper.selectSurveyBasicInfo(paperId);
            debugInfo.put("basicInfo", basicInfo);
            log.info("调试：问卷基本信息: {}", basicInfo);
            
            // 2. 查询题目列表
            List<TcSurveyQuestionDTO> questions = tcSurveyMapper.selectQuestionsByPaperId(paperId);
            debugInfo.put("questions", questions);
            debugInfo.put("questionCount", questions.size());
            log.info("调试：题目列表大小: {}", questions.size());
            
            for (int i = 0; i < questions.size(); i++) {
                TcSurveyQuestionDTO question = questions.get(i);
                log.info("调试：题目{}: {}", i+1, question);
                
                String questionId = question.getQuestionId();
                if (questionId != null) {
                    // 查询选项
                    List<TcSurveyOptionDTO> options = tcSurveyMapper.selectOptionsByQuestionId(questionId);
                    log.info("调试：题目{}的选项: {}", i+1, options);
                    // 注意：由于DTO不能直接put，这里可以创建一个Map来存储调试信息
                    Map<String, Object> questionDebugInfo = new HashMap<>();
                    questionDebugInfo.put("questionId", question.getQuestionId());
                    questionDebugInfo.put("questionText", question.getQuestionText());
                    questionDebugInfo.put("questionType", question.getQuestionType());
                    questionDebugInfo.put("options", options);
                    debugInfo.put("question_" + (i+1), questionDebugInfo);
                }
            }
            
            return AjaxResult.success("调试完成", debugInfo);
            
        } catch (Exception e) {
            log.error("调试查询异常", e);
            debugInfo.put("error", e.getMessage());
            return AjaxResult.error("调试查询失败: " + e.getMessage()).put("debugInfo", debugInfo);
        }
    }
} 