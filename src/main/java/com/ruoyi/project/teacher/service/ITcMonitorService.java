package com.ruoyi.project.teacher.service;

import com.ruoyi.project.teacher.vo.TcCourseInfoVO;
import com.ruoyi.project.teacher.vo.TcMonitorStatisticsVO;

/**
 * 教师端监控Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface ITcMonitorService {

    /**
     * 验证课程码是否有效
     * 
     * @param courseCode 课程码
     * @return 是否有效
     */
    boolean validateCourseCode(String courseCode);

    /**
     * 获取课程信息
     * 
     * @param courseCode 课程码
     * @return 课程信息
     */
    TcCourseInfoVO getCourseInfo(String courseCode);

    /**
     * 获取监控统计信息
     * 
     * @param courseCode 课程码（可选）
     * @return 统计信息
     */
    TcMonitorStatisticsVO getMonitorStatistics(String courseCode);
} 