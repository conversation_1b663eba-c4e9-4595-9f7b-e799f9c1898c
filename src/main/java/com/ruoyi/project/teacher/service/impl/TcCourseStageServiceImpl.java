package com.ruoyi.project.teacher.service.impl;

import com.ruoyi.project.teacher.dto.TcCourseStageDTO;
import com.ruoyi.project.teacher.mapper.TcCourseStageMapper;
import com.ruoyi.project.teacher.service.ITcCourseStageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 教师端课程阶段Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TcCourseStageServiceImpl implements ITcCourseStageService {

    private final TcCourseStageMapper tcCourseStageMapper;

    /**
     * 根据课程代码查询课程阶段列表
     * 
     * @param courseCode 课程代码
     * @return 课程阶段列表
     */
    @Override
    public List<TcCourseStageDTO> getCourseStageListByCourseCode(String courseCode) {
        log.debug("根据课程代码查询课程阶段列表，courseCode: {}", courseCode);
        
        if (!StringUtils.hasText(courseCode)) {
            log.warn("课程代码为空，返回空列表");
            return new ArrayList<>();
        }
        
        try {
            List<TcCourseStageDTO> stageList = tcCourseStageMapper.selectCourseStageListByCourseCode(courseCode);
            log.debug("查询到课程代码 {} 的阶段数量: {}", courseCode, stageList.size());
            
            return stageList;
        } catch (Exception e) {
            log.error("根据课程代码查询课程阶段列表异常，courseCode: {}", courseCode, e);
            throw new RuntimeException("查询课程阶段列表失败", e);
        }
    }

    /**
     * 根据课程ID查询课程阶段列表
     * 
     * @param courseId 课程ID
     * @return 课程阶段列表
     */
    @Override
    public List<TcCourseStageDTO> getCourseStageListByCourseId(String courseId) {
        log.debug("根据课程ID查询课程阶段列表，courseId: {}", courseId);
        
        if (!StringUtils.hasText(courseId)) {
            log.warn("课程ID为空，返回空列表");
            return new ArrayList<>();
        }
        
        try {
            List<TcCourseStageDTO> stageList = tcCourseStageMapper.selectCourseStageListByCourseId(courseId);
            log.debug("查询到课程ID {} 的阶段数量: {}", courseId, stageList.size());
            
            return stageList;
        } catch (Exception e) {
            log.error("根据课程ID查询课程阶段列表异常，courseId: {}", courseId, e);
            throw new RuntimeException("查询课程阶段列表失败", e);
        }
    }
} 