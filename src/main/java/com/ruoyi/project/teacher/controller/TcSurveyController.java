package com.ruoyi.project.teacher.controller;

import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.teacher.dto.TcSurveyListDTO;
import com.ruoyi.project.teacher.service.ITcSurveyService;
import com.ruoyi.project.teacher.vo.TcSurveyStatisticsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 教师端问卷调查Controller
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/teacher/survey")
public class TcSurveyController extends BaseController {

    private final ITcSurveyService tcSurveyService;

    /**
     * 根据课程代码查询问卷列表
     * 
     * @param courseCode 课程代码
     * @return 问卷列表
     */
    @GetMapping("/list/course/{courseCode}")
    public AjaxResult getSurveyListByCourseCode(
            @PathVariable("courseCode") 
            @NotBlank(message = "课程代码不能为空")
            String courseCode) {
        
        log.info("根据课程代码查询问卷列表: {}", courseCode);
        
        try {
            List<TcSurveyListDTO> surveyList = tcSurveyService.getSurveyListByCourseCode(courseCode);
            return AjaxResult.success(surveyList);
        } catch (Exception e) {
            log.error("查询问卷列表异常，课程代码: {}", courseCode, e);
            return AjaxResult.error("查询问卷列表失败");
        }
    }

    /**
     * 根据问卷ID查询问卷统计结果
     * 
     * @param paperId 问卷ID
     * @return 问卷统计结果
     */
    @GetMapping("/statistics/{paperId}")
    public AjaxResult getSurveyStatistics(
            @PathVariable("paperId") 
            @NotBlank(message = "问卷ID不能为空")
            String paperId) {
        
        log.info("查询问卷统计结果: {}", paperId);
        
        try {
            TcSurveyStatisticsVO statistics = tcSurveyService.getSurveyStatistics(paperId);
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            log.error("查询问卷统计结果异常，问卷ID: {}", paperId, e);
            return AjaxResult.error("查询问卷统计结果失败");
        }
    }

    /**
     * 调试方法：测试问卷题目查询
     * 
     * @param paperId 问卷ID
     * @return 调试信息
     */
    @GetMapping("/debug/questions/{paperId}")
    public AjaxResult debugQuestions(
            @PathVariable("paperId") 
            @NotBlank(message = "问卷ID不能为空")
            String paperId) {
        
        log.info("调试：查询问卷题目: {}", paperId);
        
        try {
            return tcSurveyService.debugQuestions(paperId);
        } catch (Exception e) {
            log.error("调试查询异常，问卷ID: {}", paperId, e);
            return AjaxResult.error("调试查询失败: " + e.getMessage());
        }
    }
} 