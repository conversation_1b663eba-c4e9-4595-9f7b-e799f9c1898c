package com.ruoyi.project.teacher.controller;

import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.teacher.service.ITcMonitorService;
import com.ruoyi.project.teacher.vo.TcCourseInfoVO;
import com.ruoyi.project.teacher.vo.TcMonitorStatisticsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 教师端监控管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/teacher/monitor")
public class TcMonitorController extends BaseController {

    private final ITcMonitorService tcMonitorService;

    /**
     * 验证课程码是否有效
     * 
     * @param courseCode 课程码
     * @return 验证结果
     */
    @GetMapping("/validate-course-code/{courseCode}")
    public AjaxResult validateCourseCode(
            @PathVariable("courseCode") 
            @NotBlank(message = "课程码不能为空") 
            @Pattern(regexp = "^[A-Za-z0-9_-]{3,50}$", message = "课程码格式不正确")
            String courseCode) {
        
        log.info("验证课程码: {}", courseCode);
        boolean isValid = tcMonitorService.validateCourseCode(courseCode);
        return AjaxResult.success(isValid ? "课程码验证成功" : "课程码验证失败", isValid);
    }

    /**
     * 获取课程信息
     * 
     * @param courseCode 课程码
     * @return 课程信息
     */
    @GetMapping("/course/{courseCode}")
    public AjaxResult getCourseInfo(
            @PathVariable("courseCode") 
            @NotBlank(message = "课程码不能为空")
            String courseCode) {
        
        log.info("获取课程信息: {}", courseCode);
        
        try {
            TcCourseInfoVO courseInfo = tcMonitorService.getCourseInfo(courseCode);
            
            if (courseInfo != null) {
                return AjaxResult.success(courseInfo);
            } else {
                return AjaxResult.error("课程信息不存在");
            }
        } catch (Exception e) {
            log.error("获取课程信息异常: {}", courseCode, e);
            return AjaxResult.error("获取课程信息失败");
        }
    }

    /**
     * 获取监控统计信息
     * 
     * @param courseCode 课程码（可选）
     * @return 统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getMonitorStatistics(
            @RequestParam(value = "courseCode", required = false) String courseCode) {
        
        log.info("获取监控统计信息，课程码: {}", courseCode);
        
        try {
            TcMonitorStatisticsVO statistics = tcMonitorService.getMonitorStatistics(courseCode);
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            log.error("获取监控统计信息异常，课程码: {}", courseCode, e);
            return AjaxResult.error("获取统计信息失败");
        }
    }
} 