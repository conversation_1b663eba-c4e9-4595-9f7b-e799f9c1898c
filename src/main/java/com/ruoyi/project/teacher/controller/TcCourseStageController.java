package com.ruoyi.project.teacher.controller;

import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.teacher.dto.TcCourseStageDTO;
import com.ruoyi.project.teacher.service.ITcCourseStageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 教师端课程阶段Controller
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/teacher/course/stage")
public class TcCourseStageController extends BaseController {

    private final ITcCourseStageService tcCourseStageService;

    /**
     * 根据课程代码查询课程阶段列表
     * 
     * @param courseCode 课程代码
     * @return 课程阶段列表
     */
    @GetMapping("/list/course/{courseCode}")
    public AjaxResult getCourseStageListByCourseCode(
            @PathVariable("courseCode") 
            @NotBlank(message = "课程代码不能为空")
            String courseCode) {
        
        log.info("根据课程代码查询课程阶段列表: {}", courseCode);
        
        try {
            List<TcCourseStageDTO> stageList = tcCourseStageService.getCourseStageListByCourseCode(courseCode);
            return AjaxResult.success(stageList);
        } catch (Exception e) {
            log.error("查询课程阶段列表异常，课程代码: {}", courseCode, e);
            return AjaxResult.error("查询课程阶段列表失败");
        }
    }

    /**
     * 根据课程ID查询课程阶段列表
     * 
     * @param courseId 课程ID
     * @return 课程阶段列表
     */
    @GetMapping("/list/{courseId}")
    public AjaxResult getCourseStageListByCourseId(
            @PathVariable("courseId") 
            @NotBlank(message = "课程ID不能为空")
            String courseId) {
        
        log.info("根据课程ID查询课程阶段列表: {}", courseId);
        
        try {
            List<TcCourseStageDTO> stageList = tcCourseStageService.getCourseStageListByCourseId(courseId);
            return AjaxResult.success(stageList);
        } catch (Exception e) {
            log.error("查询课程阶段列表异常，课程ID: {}", courseId, e);
            return AjaxResult.error("查询课程阶段列表失败");
        }
    }
} 