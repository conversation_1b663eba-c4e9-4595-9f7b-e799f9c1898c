package com.ruoyi.project.teacher.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;

/**
 * 教师端问卷选项DTO
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TcSurveyOptionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 题目ID
     */
    private String questionId;

    /**
     * 选项ID
     */
    private String itemId;

    /**
     * 选项文本
     */
    private String itemText;

    /**
     * 是否正确答案（Y-是，N-否）
     */
    private String isRight;
} 