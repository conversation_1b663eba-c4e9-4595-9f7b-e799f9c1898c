package com.ruoyi.project.teacher.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 教师端课程阶段DTO
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@NoArgsConstructor 
@AllArgsConstructor
public class TcCourseStageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 课程阶段ID
     */
    private String courseStageId;

    /**
     * 课程ID
     */
    private String courseId;

    /**
     * 课程阶段标题
     */
    private String courseStageTitle;

    /**
     * 课程阶段内容
     */
    private String courseStageText;

    /**
     * 课程阶段顺序
     */
    private Integer courseStageOrder;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 是否已完成（0:未完成, 1:已完成）
     */
    private Integer isCompleted;

    /**
     * 阶段附件列表
     */
    private List<TcCourseStageAnnexDTO> stageAnnexList;

    /**
     * 阶段状态（可扩展：进行中、已完成等）
     */
    private String stageStatus;
} 