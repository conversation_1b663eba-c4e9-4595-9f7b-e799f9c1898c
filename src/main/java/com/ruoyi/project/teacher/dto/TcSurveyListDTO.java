package com.ruoyi.project.teacher.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 教师端问卷列表DTO
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TcSurveyListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 问卷ID
     */
    private String paperId;

    /**
     * 问卷标题
     */
    private String paperTitle;

    /**
     * 场景ID
     */
    private String sceneId;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 课程ID
     */
    private String courseId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 课程代码
     */
    private String courseCode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 总学生数
     */
    private Integer totalStudents;

    /**
     * 已提交数量
     */
    private Integer submittedCount;

    /**
     * 提交率（百分比）
     */
    private Double submissionRate;

    /**
     * 问卷题目数量
     */
    private Integer questionCount;
} 