package com.ruoyi.project.teacher.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;

/**
 * 教师端问卷题目DTO
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TcSurveyQuestionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 题目ID
     */
    private String questionId;

    /**
     * 题目文本
     */
    private String questionText;

    /**
     * 题目类型（1-单选，2-多选）
     */
    private Integer questionType;
} 