package com.ruoyi.project.teacher.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 教师端课程阶段附件DTO
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@NoArgsConstructor 
@AllArgsConstructor
public class TcCourseStageAnnexDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 附件ID
     */
    private String annexId;

    /**
     * 阶段ID
     */
    private String stageId;

    /**
     * 附件名称
     */
    private String annexName;

    /**
     * 附件路径
     */
    private String annexPath;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
} 