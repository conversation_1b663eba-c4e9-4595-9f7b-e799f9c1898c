package com.ruoyi.project.student.websocket.support;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.project.student.websocket.WebSocketConstants;
import com.ruoyi.project.student.websocket.vo.GroupUserInfo;
import com.ruoyi.project.student.websocket.vo.OnlineUserInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Redis 用户连接状态管理器
 * 负责维护全局在线用户和群组用户的 Redis 缓存
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RedisUserConnectionManager {

    private final StringRedisTemplate stringRedisTemplate;
    private final ObjectMapper objectMapper;

    // ============== 全局在线用户管理 ==============

    /**
     * 用户上线 - 添加到全局在线用户列表
     *
     * @param userId 用户ID
     */
    public void userOnline(Long userId) {
        if (userId == null) {
            log.warn("[用户连接状态] 用户上线时用户ID为空，跳过处理");
            return;
        }

        try {
            OnlineUserInfo onlineUserInfo = OnlineUserInfo.create(userId);
            String json = objectMapper.writeValueAsString(onlineUserInfo);
            
            stringRedisTemplate.opsForHash().put(
                WebSocketConstants.REDIS_ONLINE_USERS_KEY, 
                userId.toString(), 
                json
            );
            
            log.debug("[用户连接状态] 用户 {} 已标记为在线", userId);
        } catch (JsonProcessingException e) {
            log.error("[用户连接状态] 序列化用户在线信息失败: userId={}", userId, e);
        } catch (Exception e) {
            log.error("[用户连接状态] 用户上线处理失败: userId={}", userId, e);
        }
    }

    /**
     * 用户下线 - 从全局在线用户列表移除
     *
     * @param userId 用户ID
     */
    public void userOffline(Long userId) {
        if (userId == null) {
            log.warn("[用户连接状态] 用户下线时用户ID为空，跳过处理");
            return;
        }

        try {
            Long removed = stringRedisTemplate.opsForHash().delete(
                WebSocketConstants.REDIS_ONLINE_USERS_KEY, 
                userId.toString()
            );
            
            if (removed > 0) {
                log.debug("[用户连接状态] 用户 {} 已从在线列表移除", userId);
            } else {
                log.debug("[用户连接状态] 用户 {} 不在在线列表中", userId);
            }
        } catch (Exception e) {
            log.error("[用户连接状态] 用户下线处理失败: userId={}", userId, e);
        }
    }

    /**
     * 检查用户是否在线
     *
     * @param userId 用户ID
     * @return 是否在线
     */
    public boolean isUserOnline(Long userId) {
        if (userId == null) {
            return false;
        }

        try {
            String json = (String) stringRedisTemplate.opsForHash().get(
                WebSocketConstants.REDIS_ONLINE_USERS_KEY, 
                userId.toString()
            );
            return json != null;
        } catch (Exception e) {
            log.error("[用户连接状态] 检查用户在线状态失败: userId={}", userId, e);
            return false;
        }
    }

    /**
     * 获取所有在线用户信息
     *
     * @return 在线用户信息列表
     */
    public List<OnlineUserInfo> getAllOnlineUsers() {
        try {
            Map<Object, Object> onlineUsersMap = stringRedisTemplate.opsForHash().entries(WebSocketConstants.REDIS_ONLINE_USERS_KEY);
            if (onlineUsersMap == null || onlineUsersMap.isEmpty()) {
                return new ArrayList<>();
            }

            return onlineUsersMap.values().stream()
                    .map(value -> this.parseOnlineUserInfo((String) value))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("[用户连接状态] 获取所有在线用户失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取在线用户数量
     *
     * @return 在线用户数量
     */
    public int getOnlineUserCount() {
        try {
            Long size = stringRedisTemplate.opsForHash().size(WebSocketConstants.REDIS_ONLINE_USERS_KEY);
            return size != null ? size.intValue() : 0;
        } catch (Exception e) {
            log.error("[用户连接状态] 获取在线用户数量失败", e);
            return 0;
        }
    }

    // ============== 群组用户管理 ==============

    /**
     * 用户加入群组
     *
     * @param groupId 群组ID
     * @param userId 用户ID
     * @param username 用户名
     * @param nickname 昵称
     * @param avatar 头像
     * @param sessionId 会话ID
     */
    public void userJoinGroup(String groupId, Long userId, String username, String nickname, String avatar, String sessionId) {
        if (groupId == null || userId == null) {
            log.warn("[用户连接状态] 用户加入群组时参数不完整，跳过处理: groupId={}, userId={}", groupId, userId);
            return;
        }

        try {
            GroupUserInfo groupUserInfo = GroupUserInfo.create(userId, username, nickname, avatar, sessionId);
            String json = objectMapper.writeValueAsString(groupUserInfo);
            
            String groupKey = WebSocketConstants.getGroupUsersKey(groupId);
            stringRedisTemplate.opsForHash().put(groupKey, userId.toString(), json);

            List<Object> values = stringRedisTemplate.opsForHash().values(groupKey);
            log.info(JSONUtil.toJsonStr(values));

            // 设置群组键的过期时间，防止僵尸群组数据
            stringRedisTemplate.expire(groupKey, WebSocketConstants.USER_CONNECTION_CACHE_EXPIRE_SECONDS, TimeUnit.SECONDS);
            
            log.debug("[用户连接状态] 用户 {} 已加入群组 {}", userId, groupId);
        } catch (JsonProcessingException e) {
            log.error("[用户连接状态] 序列化群组用户信息失败: groupId={}, userId={}", groupId, userId, e);
        } catch (Exception e) {
            log.error("[用户连接状态] 用户加入群组失败: groupId={}, userId={}", groupId, userId, e);
        }
    }

    /**
     * 用户离开群组
     *
     * @param groupId 群组ID
     * @param userId 用户ID
     */
    public void userLeaveGroup(String groupId, Long userId) {
        if (groupId == null || userId == null) {
            log.warn("[用户连接状态] 用户离开群组时参数不完整，跳过处理: groupId={}, userId={}", groupId, userId);
            return;
        }

        try {
            String groupKey = WebSocketConstants.getGroupUsersKey(groupId);
            Long removed = stringRedisTemplate.opsForHash().delete(groupKey, userId.toString());
            
            if (removed > 0) {
                log.debug("[用户连接状态] 用户 {} 已离开群组 {}", userId, groupId);
            } else {
                log.debug("[用户连接状态] 用户 {} 不在群组 {} 中", userId, groupId);
            }
        } catch (Exception e) {
            log.error("[用户连接状态] 用户离开群组失败: groupId={}, userId={}", groupId, userId, e);
        }
    }

    /**
     * 获取群组中的所有在线用户
     *
     * @param groupId 群组ID
     * @return 群组用户信息列表
     */
    public List<GroupUserInfo> getGroupUsers(String groupId) {
        if (groupId == null) {
            return new ArrayList<>();
        }

        try {
            String groupKey = WebSocketConstants.getGroupUsersKey(groupId);
            Map<Object, Object> groupUsersMap = stringRedisTemplate.opsForHash().entries(groupKey);
            
            if (groupUsersMap == null || groupUsersMap.isEmpty()) {
                return new ArrayList<>();
            }

            return groupUsersMap.values().stream()
                    .map(value -> this.parseGroupUserInfo((String) value))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("[用户连接状态] 获取群组用户失败: groupId={}", groupId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取群组在线用户数量
     *
     * @param groupId 群组ID
     * @return 群组在线用户数量
     */
    public int getGroupUserCount(String groupId) {
        if (groupId == null) {
            return 0;
        }

        try {
            String groupKey = WebSocketConstants.getGroupUsersKey(groupId);
            Long size = stringRedisTemplate.opsForHash().size(groupKey);
            return size != null ? size.intValue() : 0;
        } catch (Exception e) {
            log.error("[用户连接状态] 获取群组用户数量失败: groupId={}", groupId, e);
            return 0;
        }
    }

    /**
     * 检查用户是否在群组中
     *
     * @param groupId 群组ID
     * @param userId 用户ID
     * @return 是否在群组中
     */
    public boolean isUserInGroup(String groupId, Long userId) {
        if (groupId == null || userId == null) {
            return false;
        }

        try {
            String groupKey = WebSocketConstants.getGroupUsersKey(groupId);
            String json = (String) stringRedisTemplate.opsForHash().get(groupKey, userId.toString());
            return json != null;
        } catch (Exception e) {
            log.error("[用户连接状态] 检查用户群组状态失败: groupId={}, userId={}", groupId, userId, e);
            return false;
        }
    }

    /**
     * 清空群组所有用户
     *
     * @param groupId 群组ID
     */
    public void clearGroupUsers(String groupId) {
        if (groupId == null) {
            return;
        }

        try {
            String groupKey = WebSocketConstants.getGroupUsersKey(groupId);
            stringRedisTemplate.delete(groupKey);
            log.debug("[用户连接状态] 已清空群组 {} 的所有用户", groupId);
        } catch (Exception e) {
            log.error("[用户连接状态] 清空群组用户失败: groupId={}", groupId, e);
        }
    }

    /**
     * 清理用户在所有群组中的状态（用户断开连接时调用）
     *
     * @param userId 用户ID
     */
    public void clearUserFromAllGroups(Long userId) {
        if (userId == null) {
            return;
        }

        try {
            // 获取所有群组键的模式
            String groupPattern = WebSocketConstants.REDIS_GROUP_USERS_KEY_PREFIX + "*";
            Set<String> groupKeys = stringRedisTemplate.keys(groupPattern);
            
            if (groupKeys != null && !groupKeys.isEmpty()) {
                for (String groupKey : groupKeys) {
                    // 从每个群组中移除该用户
                    Long removed = stringRedisTemplate.opsForHash().delete(groupKey, userId.toString());
                    if (removed > 0) {
                        String groupId = groupKey.replace(WebSocketConstants.REDIS_GROUP_USERS_KEY_PREFIX, "");
                        log.debug("[用户连接状态] 用户 {} 已从群组 {} 中移除", userId, groupId);
                    }
                }
            }
            
            log.debug("[用户连接状态] 已清理用户 {} 在所有群组中的状态", userId);
        } catch (Exception e) {
            log.error("[用户连接状态] 清理用户在所有群组中的状态失败: userId={}", userId, e);
        }
    }

    // ============== 系统管理 ==============

    /**
     * 清理所有WebSocket相关的Redis缓存
     * 通常在系统启动时调用，确保清理上次关闭时残留的缓存数据
     */
    public void clearAllWebSocketCache() {
        try {
            // 1. 清理全局在线用户缓存
            stringRedisTemplate.delete(WebSocketConstants.REDIS_ONLINE_USERS_KEY);
            log.info("[用户连接状态] 已清理全局在线用户缓存");

            // 2. 清理所有群组用户缓存
            String groupPattern = WebSocketConstants.REDIS_GROUP_USERS_KEY_PREFIX + "*";
            Set<String> groupKeys = stringRedisTemplate.keys(groupPattern);
            
            if (groupKeys != null && !groupKeys.isEmpty()) {
                stringRedisTemplate.delete(groupKeys);
                log.info("[用户连接状态] 已清理 {} 个群组的用户缓存", groupKeys.size());
            } else {
                log.info("[用户连接状态] 未发现需要清理的群组用户缓存");
            }

            // 3. 清理聊天消息队列（可选，根据需要决定是否清理）
            Long queueSize = stringRedisTemplate.opsForList().size(WebSocketConstants.REDIS_CHAT_MESSAGE_QUEUE_KEY);
            if (queueSize != null && queueSize > 0) {
                stringRedisTemplate.delete(WebSocketConstants.REDIS_CHAT_MESSAGE_QUEUE_KEY);
                log.info("[用户连接状态] 已清理聊天消息队列，共 {} 条消息", queueSize);
            }

            log.info("[用户连接状态] WebSocket 缓存清理完成");
        } catch (Exception e) {
            log.error("[用户连接状态] 清理 WebSocket 缓存时发生错误", e);
            throw new RuntimeException("清理 WebSocket 缓存失败", e);
        }
    }

    /**
     * 获取所有WebSocket缓存的统计信息
     * 用于监控和调试
     */
    public Map<String, Object> getWebSocketCacheStats() {
        try {
            Map<String, Object> stats = new HashMap<>();

            // 全局在线用户数量
            Long onlineUserCount = stringRedisTemplate.opsForHash().size(WebSocketConstants.REDIS_ONLINE_USERS_KEY);
            stats.put("onlineUserCount", onlineUserCount != null ? onlineUserCount : 0);

            // 群组数量和总成员数
            String groupPattern = WebSocketConstants.REDIS_GROUP_USERS_KEY_PREFIX + "*";
            Set<String> groupKeys = stringRedisTemplate.keys(groupPattern);
            int groupCount = groupKeys != null ? groupKeys.size() : 0;
            stats.put("groupCount", groupCount);

            int totalGroupMembers = 0;
            if (groupKeys != null) {
                for (String groupKey : groupKeys) {
                    Long memberCount = stringRedisTemplate.opsForHash().size(groupKey);
                    totalGroupMembers += memberCount != null ? memberCount.intValue() : 0;
                }
            }
            stats.put("totalGroupMembers", totalGroupMembers);

            // 聊天消息队列长度
            Long queueSize = stringRedisTemplate.opsForList().size(WebSocketConstants.REDIS_CHAT_MESSAGE_QUEUE_KEY);
            stats.put("messageQueueSize", queueSize != null ? queueSize : 0);

            return stats;
        } catch (Exception e) {
            log.error("[用户连接状态] 获取 WebSocket 缓存统计信息失败", e);
            return new HashMap<>();
        }
    }

    // ============== 工具方法 ==============

    /**
     * 解析在线用户信息 JSON
     */
    private OnlineUserInfo parseOnlineUserInfo(String json) {
        try {
            return objectMapper.readValue(json, OnlineUserInfo.class);
        } catch (JsonProcessingException e) {
            log.warn("[用户连接状态] 解析在线用户信息失败: {}", json, e);
            return null;
        }
    }

    /**
     * 解析群组用户信息 JSON
     */
    private GroupUserInfo parseGroupUserInfo(String json) {
        try {
            return objectMapper.readValue(json, GroupUserInfo.class);
        } catch (JsonProcessingException e) {
            log.warn("[用户连接状态] 解析群组用户信息失败: {}", json, e);
            return null;
        }
    }
} 