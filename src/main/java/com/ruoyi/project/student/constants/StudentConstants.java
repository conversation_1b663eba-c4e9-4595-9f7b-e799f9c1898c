package com.ruoyi.project.student.constants;

/**
 * 学生端常量定义
 * 
 * <AUTHOR>
 */
public class StudentConstants {
    
    /**
     * 马甲图标相关常量
     */
    public static class PuppetIcon {
        
        /** 马甲图标接口路径 */
        public static final String ICON_API_PATH = "/student/puppet/icon/";
        
        /** 图片文件扩展名 */
        public static final String IMAGE_EXTENSION = ".png";
        
        /** 有效文件名正则表达式（支持中文、英文、数字、下划线、连字符） */
        public static final String VALID_FILENAME_PATTERN = "^[a-zA-Z0-9_\\-\\u4e00-\\u9fa5]{1,50}$";
        
        /** 批量处理最大数量 */
        public static final int BATCH_MAX_SIZE = 50;
        
        /** URL编码字符集 */
        public static final String URL_ENCODING = "UTF-8";
    }
    
    /**
     * 缓存相关常量
     */
    public static class Cache {
        
        /** 马甲图标缓存前缀 */
        public static final String PUPPET_ICON_PREFIX = "puppet_icon:";
        
        /** 马甲配置缓存前缀 */
        public static final String PUPPET_CONFIG_PREFIX = "puppet_config:";
        
        /** 图标缓存过期时间（小时） */
        public static final int ICON_CACHE_EXPIRE_HOURS = 24;
        
        /** 配置缓存过期时间（分钟） */
        public static final int CONFIG_CACHE_EXPIRE_MINUTES = 30;
    }
} 