package com.ruoyi.project.student.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.student.domain.StSysUser;
import com.ruoyi.project.student.domain.dto.StudentCourseInfoDTO;
import com.ruoyi.project.student.dto.GroupUserDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学生端系统用户 数据层
 *
 * <AUTHOR>
 */
@Mapper
public interface StSysUserMapper extends BaseMapper<StSysUser> {
    
    /**
     * 查询学生课程信息
     *
     * @param userName 用户名
     * @param userId 用户ID
     * @return 学生课程信息
     */
    List<StudentCourseInfoDTO> selectStudentCourseInfo(@Param("userName") String userName, @Param("userId") Long userId);

    /**
     * 根据课程分组ID查询分组内的用户信息（包含马甲信息）
     *
     * @param groupId 课程分组ID
     * @return 用户信息列表 (包含马甲)
     */
    List<GroupUserDTO> selectGroupUsersByGroupId(@Param("groupId") String groupId);

    /**
     * 查询当前用户所在班级的课程群信息
     *
     * @param userName 用户名
     * @param userId 用户ID
     * @return 课程群信息列表
     */
    List<StudentCourseInfoDTO> selectStudentCourseGroupInfo(@Param("userName") String userName, @Param("userId") Long userId);

    /**
     * 根据课程ID查询该课程下所有学生信息（用于课程群）
     *
     * @param courseId 课程ID
     * @return 课程下的学生信息列表
     */
    List<GroupUserDTO> selectCourseUsersByCourseId(@Param("courseId") String courseId);
} 