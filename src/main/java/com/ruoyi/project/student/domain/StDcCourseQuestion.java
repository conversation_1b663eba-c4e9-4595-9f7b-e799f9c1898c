package com.ruoyi.project.student.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.project.scenario.domain.DcCourseQuestion;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 学生端课程试题实体类
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("DC_COURSE_QUESTION")
public class StDcCourseQuestion extends DcCourseQuestion {

    /**
     * 题目的选项列表（关联查询）
     */
    @TableField(exist = false)
    private List<StDcCourseQuestionItem> questionItems;
}
