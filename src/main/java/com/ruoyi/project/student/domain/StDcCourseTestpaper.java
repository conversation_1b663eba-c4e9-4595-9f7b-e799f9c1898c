package com.ruoyi.project.student.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.project.scenario.domain.DcCourseTestpaper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 学生端课程问卷调查实体类
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("DC_COURSE_TESTPAPER")
public class StDcCourseTestpaper extends DcCourseTestpaper {

    /**
     * 问卷的题目列表（关联查询）
     */
    @TableField(exist = false)
    private List<StDcCourseQuestion> questions;
}
