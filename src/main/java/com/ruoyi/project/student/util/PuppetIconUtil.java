package com.ruoyi.project.student.util;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.project.student.dto.GroupUserDTO;
import com.ruoyi.project.student.domain.dto.StudentCourseInfoDTO;
import com.ruoyi.project.student.service.IPuppetIconService;
import com.ruoyi.project.student.constants.StudentConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 马甲图标处理工具类
 * 提供批量处理马甲图标URL拼接的工具方法
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class PuppetIconUtil {

    @Autowired
    private IPuppetIconService puppetIconService;

    /**
     * 批量处理学生课程信息中的马甲图标
     * 
     * @param courseInfoList 学生课程信息列表
     */
    public void processCourseInfoPuppetIcons(List<StudentCourseInfoDTO> courseInfoList) {
        if (courseInfoList == null || courseInfoList.isEmpty()) {
            return;
        }

        for (StudentCourseInfoDTO courseInfo : courseInfoList) {
            processSingleCourseInfoPuppetIcon(courseInfo);
        }
    }

    /**
     * 处理单个学生课程信息中的马甲图标
     * 
     * @param courseInfo 学生课程信息
     */
    public void processSingleCourseInfoPuppetIcon(StudentCourseInfoDTO courseInfo) {
        if (courseInfo == null) {
            return;
        }

        try {
            String puppetIcon = courseInfo.getPuppetIcon();
            if (StringUtils.isNotEmpty(puppetIcon)) {
                String iconUrl = puppetIconService.buildPuppetIconUrlSafely(puppetIcon);
                courseInfo.setPuppetIcon(iconUrl);
                log.debug("[PuppetIconUtil] 课程信息马甲图标处理完成: {} -> {}", puppetIcon, iconUrl);
            }
        } catch (Exception e) {
            log.error("[PuppetIconUtil] 处理课程信息马甲图标失败", e);
        }
    }

    /**
     * 批量处理分组用户信息中的马甲图标
     * 
     * @param groupUsers 分组用户信息列表
     */
    public void processGroupUserPuppetIcons(List<GroupUserDTO> groupUsers) {
        if (groupUsers == null || groupUsers.isEmpty()) {
            return;
        }

        for (GroupUserDTO user : groupUsers) {
            processSingleGroupUserPuppetIcon(user);
        }
    }

    /**
     * 处理单个分组用户信息中的马甲图标
     * 
     * @param user 分组用户信息
     */
    public void processSingleGroupUserPuppetIcon(GroupUserDTO user) {
        if (user == null) {
            return;
        }

        try {
            String puppetIcon = user.getPuppetIcon();
            if (StringUtils.isNotEmpty(puppetIcon)) {
                String iconUrl = puppetIconService.buildPuppetIconUrlSafely(puppetIcon);
                user.setPuppetIcon(iconUrl);
                log.debug("[PuppetIconUtil] 分组用户马甲图标处理完成: {} -> {}", puppetIcon, iconUrl);
            }
        } catch (Exception e) {
            log.error("[PuppetIconUtil] 处理分组用户马甲图标失败", e);
        }
    }

    /**
     * 检查马甲图标文件名是否有效
     * 
     * @param iconFileName 图标文件名
     * @return 是否有效
     */
    public static boolean isValidIconFileName(String iconFileName) {
        if (StringUtils.isEmpty(iconFileName)) {
            return false;
        }
        
        // 支持中文、字母、数字、下划线、连字符，长度限制在1-50字符
        return iconFileName.matches(StudentConstants.PuppetIcon.VALID_FILENAME_PATTERN);
    }

    /**
     * 安全地构建马甲图标URL
     * 
     * @param iconFileName 图标文件名
     * @return 图标URL，如果文件名无效则返回null
     */
    public String buildIconUrlSafely(String iconFileName) {
        if (!isValidIconFileName(iconFileName)) {
            log.warn("[PuppetIconUtil] 无效的图标文件名: {}", iconFileName);
            return null;
        }

        try {
            return puppetIconService.buildPuppetIconUrlSafely(iconFileName);
        } catch (Exception e) {
            log.error("[PuppetIconUtil] 构建图标URL失败: {}", iconFileName, e);
            return null;
        }
    }
} 