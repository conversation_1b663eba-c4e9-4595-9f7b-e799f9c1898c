package com.ruoyi.project.student.service;

/**
 * 学生端马甲图标服务接口
 * 
 * <AUTHOR>
 */
public interface IPuppetIconService {

    /**
     * 获取马甲图标文件字节数组（支持缓存）
     * 
     * @param iconFileName 图标文件名（不含后缀）
     * @return 图标文件字节数组
     */
    byte[] getPuppetIconBytes(String iconFileName);

    /**
     * 拼接马甲图标的完整URL
     * 
     * @param iconFileName 图标文件名（不含后缀）
     * @return 完整的图标访问URL
     */
    String buildPuppetIconUrl(String iconFileName);

    /**
     * 批量处理马甲图标URL拼接
     * 
     * @param iconFileName 图标文件名（不含后缀）
     * @return 完整的图标访问URL，如果文件名为空则返回null
     */
    String buildPuppetIconUrlSafely(String iconFileName);
} 