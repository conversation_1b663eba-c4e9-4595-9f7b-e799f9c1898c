package com.ruoyi.project.student.service.impl;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.project.student.constants.StudentConstants;
import com.ruoyi.project.student.service.IPuppetIconService;
import com.ruoyi.project.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

/**
 * 学生端马甲图标服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PuppetIconServiceImpl implements IPuppetIconService {

    private final ISysConfigService configService;
    private final StringRedisTemplate stringRedisTemplate;

    // 使用常量类
    private static final String CACHE_KEY_PREFIX = StudentConstants.Cache.PUPPET_ICON_PREFIX;
    private static final String CACHE_CONFIG_PREFIX = StudentConstants.Cache.PUPPET_CONFIG_PREFIX;
    private static final long CACHE_EXPIRE_HOURS = StudentConstants.Cache.ICON_CACHE_EXPIRE_HOURS;
    private static final long CONFIG_CACHE_EXPIRE_MINUTES = StudentConstants.Cache.CONFIG_CACHE_EXPIRE_MINUTES;

    @Override
    public byte[] getPuppetIconBytes(String iconFileName) {
        if (StringUtils.isEmpty(iconFileName)) {
            log.warn("[马甲图标] 图标文件名为空");
            return null;
        }

        // 1. 先从缓存获取
        String cacheKey = CACHE_KEY_PREFIX + iconFileName;
        String cachedBase64 = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotEmpty(cachedBase64)) {
            try {
                byte[] cachedBytes = Base64.getDecoder().decode(cachedBase64);
                log.debug("[马甲图标] 从缓存获取图标: {}", iconFileName);
                return cachedBytes;
            } catch (IllegalArgumentException e) {
                log.warn("[马甲图标] Base64解码失败，清除缓存: {}", iconFileName, e);
                stringRedisTemplate.delete(cacheKey);
            }
        }

        // 2. 从文件系统读取
        byte[] fileBytes = readIconFromFile(iconFileName);
        if (fileBytes != null) {
            // 3. 存入缓存（使用Base64编码）
            try {
                String base64Data = Base64.getEncoder().encodeToString(fileBytes);
                stringRedisTemplate.opsForValue().set(cacheKey, base64Data, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
                log.debug("[马甲图标] 图标已缓存: {}", iconFileName);
            } catch (Exception e) {
                log.warn("[马甲图标] 缓存图标失败: {}", iconFileName, e);
            }
        }

        return fileBytes;
    }

    @Override
    public String buildPuppetIconUrl(String iconFileName) {
        if (StringUtils.isEmpty(iconFileName)) {
            return null;
        }

        try {
            // 获取系统API地址（带缓存）
            String apiUrl = getCachedConfig("sys.api.url");
            if (StringUtils.isEmpty(apiUrl)) {
                log.error("[马甲图标] 无法获取系统API地址配置");
                return null;
            }

            // 对文件名进行URL编码（支持中文字符）
            String encodedFileName = URLEncoder.encode(iconFileName, StudentConstants.PuppetIcon.URL_ENCODING);
            
            // 拼接完整URL
            String fullUrl = apiUrl + StudentConstants.PuppetIcon.ICON_API_PATH + encodedFileName;
            log.debug("[马甲图标] 构建图标URL: {} -> {}", iconFileName, fullUrl);
            
            return fullUrl;
            
        } catch (UnsupportedEncodingException e) {
            log.error("[马甲图标] URL编码失败，iconFileName: {}", iconFileName, e);
            return null;
        }
    }

    @Override
    public String buildPuppetIconUrlSafely(String iconFileName) {
        try {
            return buildPuppetIconUrl(iconFileName);
        } catch (Exception e) {
            log.error("[马甲图标] 构建图标URL失败，iconFileName: {}", iconFileName, e);
            return null;
        }
    }

    /**
     * 从文件系统读取图标文件
     */
    private byte[] readIconFromFile(String iconFileName) {
        try {
            // 获取图标存放路径
            String iconLocation = getCachedConfig("biz.puppet.icon.location");
            if (StringUtils.isEmpty(iconLocation)) {
                log.error("[马甲图标] 无法获取图标存放路径配置");
                return null;
            }

            // 构建完整文件路径
            String filePath = iconLocation + File.separator + iconFileName + StudentConstants.PuppetIcon.IMAGE_EXTENSION;
            File iconFile = new File(filePath);

            // 安全检查：防止路径遍历攻击
            if (!isValidFilePath(iconFile, iconLocation)) {
                log.warn("[马甲图标] 检测到非法文件路径: {}", filePath);
                return null;
            }

            // 检查文件是否存在
            if (!iconFile.exists() || !iconFile.isFile()) {
                log.warn("[马甲图标] 图标文件不存在: {}", filePath);
                return null;
            }

            // 读取文件
            try (FileInputStream fis = new FileInputStream(iconFile)) {
                return fis.readAllBytes();
            }

        } catch (IOException e) {
            log.error("[马甲图标] 读取图标文件失败，iconFileName: {}", iconFileName, e);
            return null;
        }
    }

    /**
     * 带缓存的配置获取
     */
    private String getCachedConfig(String configKey) {
        String cacheKey = CACHE_CONFIG_PREFIX + configKey;
        
        // 先从缓存获取
        String cachedValue = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotEmpty(cachedValue)) {
            return cachedValue;
        }

        // 从数据库获取
        String configValue = configService.selectConfigByKey(configKey);
        if (StringUtils.isNotEmpty(configValue)) {
            // 存入缓存
            stringRedisTemplate.opsForValue().set(cacheKey, configValue, CONFIG_CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        }

        return configValue;
    }

    /**
     * 安全检查：验证文件路径是否合法，防止路径遍历攻击
     */
    private boolean isValidFilePath(File file, String baseDirectory) {
        try {
            String canonicalFilePath = file.getCanonicalPath();
            String canonicalBasePath = new File(baseDirectory).getCanonicalPath();
            
            // 确保文件路径在基础目录内
            return canonicalFilePath.startsWith(canonicalBasePath);
        } catch (IOException e) {
            log.error("[马甲图标] 文件路径安全检查失败", e);
            return false;
        }
    }
} 