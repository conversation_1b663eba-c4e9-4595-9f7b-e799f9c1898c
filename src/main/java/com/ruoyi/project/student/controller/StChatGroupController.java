package com.ruoyi.project.student.controller;

import com.ruoyi.framework.aspectj.lang.annotation.Anonymous;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.student.websocket.ChatMessage;
import com.ruoyi.project.student.service.StChatGroupService;
import com.ruoyi.project.student.service.IStSysUserService;
import com.ruoyi.project.student.domain.dto.StudentCourseInfoDTO;
import com.ruoyi.project.student.util.StAuthUtil;
import com.ruoyi.project.student.domain.StLoginUser;
import com.ruoyi.project.student.dto.GroupUserDTO;
import com.ruoyi.project.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Anonymous
@RestController
@RequestMapping("/student/chat")
public class StChatGroupController {

    @Autowired
    private StChatGroupService chatGroupService;

    @Autowired
    private IStSysUserService stSysUserService;

    @Autowired
    private ISysConfigService configService;

    @GetMapping("/messages/{groupId}")
    public AjaxResult getGroupMessages(
            @PathVariable String groupId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "50") Integer pageSize) {
        List<ChatMessage> messages = chatGroupService.getGroupMessages(groupId, pageNum, pageSize);
        return AjaxResult.success(messages);
    }

    /**
     * 获取当前登录学生的课程及分组信息
     * @return AjaxResult 包含 StudentCourseInfoDTO
     */
    @GetMapping("/currentUser/courseInfo")
    public AjaxResult getCurrentUserCourseInfo() {
        // 检查用户是否已登录，未登录会抛出401异常
        StLoginUser loginUser = StAuthUtil.getRequiredLoginUser();
        List<StudentCourseInfoDTO> courseInfo = stSysUserService.getStudentCourseInfo();
        return AjaxResult.success(courseInfo);
    }

    /**
     * 获取群组轮询配置
     * @return AjaxResult 包含轮询开关配置
     */
    @GetMapping("/config/polling")
    public AjaxResult getPollingConfig() {
        String pollingEnabled = configService.selectConfigByKey("student.group.polling.enabled");
        boolean enabled = "true".equalsIgnoreCase(pollingEnabled);
        return AjaxResult.success("获取配置成功", enabled);
    }

    /**
     * 根据课程分组ID获取该分组下的所有用户信息（包含马甲）
     *
     * @param groupId 课程分组ID
     * @return 分组用户信息列表
     */
    @GetMapping("/group/{groupId}/users")
    public AjaxResult getGroupUsers(@PathVariable String groupId) {
        List<GroupUserDTO> groupUsers = chatGroupService.getGroupUsers(groupId);
        return AjaxResult.success(groupUsers);
    }
} 