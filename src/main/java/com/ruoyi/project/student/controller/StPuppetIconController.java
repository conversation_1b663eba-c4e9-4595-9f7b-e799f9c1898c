package com.ruoyi.project.student.controller;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.aspectj.lang.annotation.Anonymous;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.student.constants.StudentConstants;
import com.ruoyi.project.student.service.IPuppetIconService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

/**
 * 学生端马甲图标控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@Anonymous
@RestController
@RequestMapping("/student/puppet")
@RequiredArgsConstructor
public class StPuppetIconController {

    private final IPuppetIconService puppetIconService;

    /**
     * 获取马甲图标文件流
     * 完整接口路径: {@value StudentConstants.PuppetIcon#ICON_API_PATH}{iconFileName}
     * 
     * @param iconFileName 图标文件名（不含.png后缀，支持URL编码的中文）
     * @return 图标文件流
     */
    @GetMapping("/icon/{iconFileName}")
    public ResponseEntity<byte[]> getPuppetIcon(@PathVariable String iconFileName) {
        log.debug("[马甲图标] 请求获取图标: {}", iconFileName);

        // 参数验证
        if (StringUtils.isEmpty(iconFileName)) {
            log.warn("[马甲图标] 图标文件名为空");
            return ResponseEntity.badRequest().build();
        }

        try {
            // URL解码处理（支持中文字符）
            String decodedFileName = URLDecoder.decode(iconFileName, StudentConstants.PuppetIcon.URL_ENCODING);
            log.debug("[马甲图标] URL解码后的文件名: {}", decodedFileName);
            
            // 安全检查：使用常量定义的正则表达式（已支持中文）
            if (!decodedFileName.matches(StudentConstants.PuppetIcon.VALID_FILENAME_PATTERN)) {
                log.warn("[马甲图标] 非法的图标文件名: {}", decodedFileName);
                return ResponseEntity.badRequest().build();
            }

            // 获取图标文件字节数组
            byte[] iconBytes = puppetIconService.getPuppetIconBytes(decodedFileName);
            
            if (iconBytes == null || iconBytes.length == 0) {
                log.warn("[马甲图标] 图标文件不存在或为空: {}", decodedFileName);
                return ResponseEntity.notFound().build();
            }

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_PNG);
            headers.setContentLength(iconBytes.length);
            // 设置缓存控制，允许浏览器缓存1小时
            headers.setCacheControl("public, max-age=3600");
            
            log.debug("[马甲图标] 成功返回图标文件: {}, 大小: {} bytes", decodedFileName, iconBytes.length);
            
            return new ResponseEntity<>(iconBytes, headers, HttpStatus.OK);

        } catch (UnsupportedEncodingException e) {
            log.error("[马甲图标] URL解码失败: {}", iconFileName, e);
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("[马甲图标] 获取图标文件失败: {}", iconFileName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 批量获取马甲图标URL（用于前端批量处理）
     * 
     * @param iconFileNames 图标文件名列表（逗号分隔，支持中文）
     * @return 图标URL映射
     */
    @GetMapping("/icon/urls")
    public AjaxResult getPuppetIconUrls(@RequestParam String iconFileNames) {
        log.debug("[马甲图标] 批量获取图标URL: {}", iconFileNames);

        try {
            if (StringUtils.isEmpty(iconFileNames)) {
                return AjaxResult.error("图标文件名不能为空");
            }

            String[] fileNames = iconFileNames.split(",");
            if (fileNames.length > StudentConstants.PuppetIcon.BATCH_MAX_SIZE) { // 限制批量请求数量
                return AjaxResult.error("一次最多处理" + StudentConstants.PuppetIcon.BATCH_MAX_SIZE + "个图标");
            }

            java.util.Map<String, String> iconUrls = new java.util.HashMap<>();
            for (String fileName : fileNames) {
                fileName = fileName.trim();
                if (StringUtils.isNotEmpty(fileName)) {
                    String iconUrl = puppetIconService.buildPuppetIconUrlSafely(fileName);
                    iconUrls.put(fileName, iconUrl);
                }
            }

            return AjaxResult.success("获取成功", iconUrls);

        } catch (Exception e) {
            log.error("[马甲图标] 批量获取图标URL失败: {}", iconFileNames, e);
            return AjaxResult.error("获取图标URL失败");
        }
    }
} 