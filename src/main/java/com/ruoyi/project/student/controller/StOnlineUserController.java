package com.ruoyi.project.student.controller;

import com.ruoyi.framework.aspectj.lang.annotation.Anonymous;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.student.websocket.support.RedisUserConnectionManager;
import com.ruoyi.project.student.websocket.vo.GroupUserInfo;
import com.ruoyi.project.student.websocket.vo.OnlineUserInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 学生端在线用户管理 Controller
 * 提供获取在线用户列表、群组成员列表等功能
 *
 * <AUTHOR>
 */
@Slf4j
@Anonymous
@RestController
@RequestMapping("/student/online")
@RequiredArgsConstructor
public class StOnlineUserController {

    private final RedisUserConnectionManager redisUserConnectionManager;

    /**
     * 获取全局在线用户列表
     *
     * @return AjaxResult 包含在线用户列表
     */
    @GetMapping("/users")
    public AjaxResult getOnlineUsers() {
        List<OnlineUserInfo> onlineUsers = redisUserConnectionManager.getAllOnlineUsers();
        int totalCount = redisUserConnectionManager.getOnlineUserCount();

        Map<String, Object> result = new HashMap<>();
        result.put("users", onlineUsers);
        result.put("totalCount", totalCount);

        log.debug("[在线用户] 获取全局在线用户列表，共 {} 人", totalCount);
        return AjaxResult.success(result);
    }

    /**
     * 获取在线用户数量
     *
     * @return AjaxResult 包含在线用户数量
     */
    @GetMapping("/users/count")
    public AjaxResult getOnlineUserCount() {
        try {
            int count = redisUserConnectionManager.getOnlineUserCount();
            log.debug("[在线用户] 获取在线用户数量: {}", count);
            return AjaxResult.success("在线用户数量", count);
        } catch (Exception e) {
            log.error("[在线用户] 获取在线用户数量失败", e);
            return AjaxResult.error("获取在线用户数量失败");
        }
    }

    /**
     * 检查用户是否在线
     *
     * @param userId 用户ID
     * @return AjaxResult 包含用户在线状态
     */
    @GetMapping("/users/{userId}/status")
    public AjaxResult getUserOnlineStatus(@PathVariable Long userId) {
        try {
            boolean isOnline = redisUserConnectionManager.isUserOnline(userId);
            Map<String, Object> result = new HashMap<>();
            result.put("userId", userId);
            result.put("isOnline", isOnline);

            log.debug("[在线用户] 检查用户 {} 在线状态: {}", userId, isOnline);
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("[在线用户] 检查用户 {} 在线状态失败", userId, e);
            return AjaxResult.error("检查用户在线状态失败");
        }
    }

    /**
     * 获取群组在线成员列表
     *
     * @param groupId 群组ID
     * @return AjaxResult 包含群组成员列表
     */
    @GetMapping("/groups/{groupId}/users")
    public AjaxResult getGroupUsers(@PathVariable String groupId) {
        List<GroupUserInfo> groupUsers = redisUserConnectionManager.getGroupUsers(groupId);
        int totalCount = redisUserConnectionManager.getGroupUserCount(groupId);

        Map<String, Object> result = new HashMap<>();
        result.put("groupId", groupId);
        result.put("users", groupUsers);
        result.put("totalCount", totalCount);

        log.debug("[在线用户] 获取群组 {} 成员列表，共 {} 人", groupId, totalCount);
        return AjaxResult.success(result);
    }

    /**
     * 获取群组在线成员数量
     *
     * @param groupId 群组ID
     * @return AjaxResult 包含群组成员数量
     */
    @GetMapping("/groups/{groupId}/users/count")
    public AjaxResult getGroupUserCount(@PathVariable String groupId) {
        int count = redisUserConnectionManager.getGroupUserCount(groupId);
        Map<String, Object> result = new HashMap<>();
        result.put("groupId", groupId);
        result.put("count", count);

        log.debug("[在线用户] 获取群组 {} 成员数量: {}", groupId, count);
        return AjaxResult.success(result);
    }

    /**
     * 检查用户是否在指定群组中
     *
     * @param groupId 群组ID
     * @param userId 用户ID
     * @return AjaxResult 包含用户群组状态
     */
    @GetMapping("/groups/{groupId}/users/{userId}/status")
    public AjaxResult getUserGroupStatus(@PathVariable String groupId, @PathVariable Long userId) {
        boolean isInGroup = redisUserConnectionManager.isUserInGroup(groupId, userId);
        Map<String, Object> result = new HashMap<>();
        result.put("groupId", groupId);
        result.put("userId", userId);
        result.put("isInGroup", isInGroup);

        log.debug("[在线用户] 检查用户 {} 在群组 {} 状态: {}", userId, groupId, isInGroup);
        return AjaxResult.success(result);
    }

    /**
     * 清空指定群组的所有用户（管理员功能）
     *
     * @param groupId 群组ID
     * @return AjaxResult
     */
    @PostMapping("/groups/{groupId}/clear")
    public AjaxResult clearGroupUsers(@PathVariable String groupId) {
        redisUserConnectionManager.clearGroupUsers(groupId);
        log.info("[在线用户] 已清空群组 {} 的所有用户", groupId);
        return AjaxResult.success("群组用户已清空");
    }

    /**
     * 清空所有WebSocket缓存（管理员功能）
     * 用于紧急情况下手动清理所有缓存
     *
     * @return AjaxResult
     */
    @PostMapping("/cache/clear")
    public AjaxResult clearAllCache() {
        redisUserConnectionManager.clearAllWebSocketCache();
        log.info("[在线用户] 已清空所有WebSocket缓存");
        return AjaxResult.success("所有WebSocket缓存已清空");
    }

    /**
     * 获取WebSocket缓存统计信息（调试功能）
     *
     * @return AjaxResult 包含缓存统计信息
     */
    @GetMapping("/cache/stats")
    public AjaxResult getCacheStats() {
        Map<String, Object> stats = redisUserConnectionManager.getWebSocketCacheStats();
        log.debug("[在线用户] 获取WebSocket缓存统计信息: {}", stats);
        return AjaxResult.success(stats);
    }

    /**
     * 手动将用户加入群组缓存（调试功能）
     * 
     * @param groupId 群组ID
     * @param userId 用户ID
     * @return AjaxResult
     */
    @PostMapping("/groups/{groupId}/users/{userId}/join")
    public AjaxResult manualJoinGroup(@PathVariable String groupId, @PathVariable Long userId) {
        try {
            // 这里需要查询用户信息，简化处理
            redisUserConnectionManager.userJoinGroup(
                groupId, 
                userId, 
                "user" + userId, 
                "用户" + userId, 
                "", 
                "manual-session-" + System.currentTimeMillis()
            );
            log.info("[在线用户] 手动将用户 {} 加入群组 {}", userId, groupId);
            return AjaxResult.success("用户已手动加入群组");
        } catch (Exception e) {
            log.error("[在线用户] 手动加入群组失败: groupId={}, userId={}", groupId, userId, e);
            return AjaxResult.error("手动加入群组失败: " + e.getMessage());
        }
    }
} 