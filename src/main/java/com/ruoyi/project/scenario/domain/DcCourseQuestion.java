package com.ruoyi.project.scenario.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 dc_course_question
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@Data
@TableName("DC_COURSE_QUESTION")
public class DcCourseQuestion implements Serializable
{
    private static final long serialVersionUID = 1L;

    @TableId(value = "QUESTION_ID", type = IdType.ASSIGN_ID)
    @Excel(name = "questionId")
    private String questionId;

    @TableField(value = "QUESTON_TEXT")
    @Excel(name = "questonText")
    private String questonText;

    @TableField(value = "QUESTION_TYPE")
    @Excel(name = "questionType")
    private String questionType;

    @TableField(value = "PAPER_ID")
    @Excel(name = "paperId")
    private String paperId;

    @TableField(value = "QUESTION_ANSWER")
    @Excel(name = "questionAnswer")
    private String questionAnswer;

    @TableField(value = "QUESTION_POINT")
    @Excel(name = "questionPoint")
    private Long questionPoint;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME")
    @Excel(name = "createTime")
    private Date createTime;

    @TableField(value = "CREATE_USER")
    @Excel(name = "createUser")
    private String createUser;

    @TableField(value = "MODIFY_USER")
    @Excel(name = "modifyUser")
    private String modifyUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "MODIFY_TIME")
    @Excel(name = "modifyTime")
    private Date modifyTime;


    @TableField(value = "QUESTION_INDEX")
    @Excel(name = "questionIndex")
    private Long questionIndex;
}
