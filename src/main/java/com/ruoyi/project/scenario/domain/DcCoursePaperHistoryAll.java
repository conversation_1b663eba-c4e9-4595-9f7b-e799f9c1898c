package com.ruoyi.project.scenario.domain;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 DC_COURSE_PAPER_HISTORY_ALL
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@TableName("DC_COURSE_PAPER_HISTORY_ALL")
public class DcCoursePaperHistoryAll implements Serializable
{
    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(value = "HISTORY_ALL_ID", type = IdType.ASSIGN_ID)
    @Excel(name = "主键")
    private String historyAllId;

    /** 问卷ID，关联DC_COURSE_TESTPAPER */
    @TableField("PAPER_ID")
    @Excel(name = "问卷ID")
    private String paperId;

    /** 课程id */
    @TableField("COURSE_ID")
    @Excel(name = "课程id")
    private String courseId;

    /** 类型，1：问卷，2：任务 */
    @TableField("PAPER_TYPE")
    @Excel(name = "类型，1：问卷，2：任务")
    private String paperType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME")
    @Excel(name = "createTime")
    private Date createTime;

    /** $column.columnComment */
    @Excel(name = "创建人")
    @TableField("CREATE_USER")
    private String createUser;

    /** $column.columnComment */
    @Excel(name = "修改人")
    @TableField("MODIFY_USER")
    private String modifyUser;

    /** $column.columnComment */
    @Excel(name = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("MODIFY_TIME")
    private Date modifyTime;

    /** 状态：1有效，0无效 */
    @Excel(name = "状态：1有效，0无效")
    @TableField("PAPER_STATE")
    private String paperState;

    /** 是否删除 */
    @Excel(name = "是否删除")
    @TableField("IS_DEL")
    private String isDel;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("PAPER_NOTES")
    private String paperNotes;

}
