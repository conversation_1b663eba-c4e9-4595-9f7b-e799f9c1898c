package com.ruoyi.project.scenario.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 dc_course_testpaper
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@Data
@TableName("DC_COURSE_TESTPAPER")
public class DcCourseTestpaper implements Serializable
{
    private static final long serialVersionUID = 1L;

    @TableId(value = "PAPER_ID", type = IdType.ASSIGN_ID)
    @Excel(name = "id")
    private String paperId;

    @TableField(value = "PAPER_TITLE")
    @Excel(name = "问卷标题")
    private String paperTitle;

    @TableField(value = "SCENE_ID")
    @Excel(name = "场景id")
    private String sceneId;

    @TableField(value = "CREATE_USER")
    @Excel(name = "createUser")
    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME")
    @Excel(name = "createTime")
    private Date createTime;

    @TableField(value = "MODIFY_USER")
    @Excel(name = "modifyUser")
    private String modifyUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "MODIFY_TIME")
    @Excel(name = "modifyTime")
    private Date modifyTime;

}
