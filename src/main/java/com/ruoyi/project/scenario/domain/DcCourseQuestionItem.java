package com.ruoyi.project.scenario.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 【请填写功能名称】对象 dc_course_question_item
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@Data
@TableName("DC_COURSE_QUESTION_ITEM")
public class DcCourseQuestionItem implements Serializable
{
    private static final long serialVersionUID = 1L;


    @TableId(value = "ITEM_ID", type = IdType.ASSIGN_ID)
    @Excel(name = "itemId")
    private String itemId;

    @TableField(value = "QUESTION_ID")
    @Excel(name = "questionId")
    private String questionId;

    @TableField(value = "ITEM_TEXT")
    @Excel(name = "itemText")
    private String itemText;

    @TableField(value = "IS_RIGHT")
    @Excel(name = "isRight")
    private String isRight;

    @TableField(value = "MODIFY_TIME")
    @Excel(name = "modifyTime")
    private Date modifyTime;

    @TableField(value = "CREATE_USER")
    @Excel(name = "createUser")
    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME")
    @Excel(name = "createTime")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "MODIFY_USER")
    @Excel(name = "modifyUser")
    private String modifyUser;

    @TableField(value = "ITEM_INDEX")
    @Excel(name = "itemIndex")
    private Integer itemIndex;
}
