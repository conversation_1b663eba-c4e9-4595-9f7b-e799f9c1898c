package com.ruoyi.project.scenario.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 场景分类对象 dc_scene_tag
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@TableName("DC_SCENE_TAG")
public class DcSceneTag extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Excel(name = "tagId")
    @TableId(value = "TAG_ID",type = IdType.ASSIGN_ID)
    private String tagId;

    /** 名称 */
    @Excel(name = "名称")
    @TableField(value = "TAG_NAME")
    private String tagName;

    /** 排序号 */
    @Excel(name = "排序号")
    @TableField(value = "TAG_INDEX")
    private Long tagIndex;

    /** 创建人 */
    @Excel(name = "创建人")
    @TableField(value = "CREATE_USER")
    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME")
    @Excel(name = "createTime")
    private Date createTime;

    /** 修改人 */
    @Excel(name = "修改人")
    @TableField(value = "MODIFY_USER")
    private String modifyUser;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "MODIFY_TIME")
    private Date modifyTime;

    @TableField(exist = false)
    private String sceneCount;
}
