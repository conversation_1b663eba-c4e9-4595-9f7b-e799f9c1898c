package com.ruoyi.project.scenario.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * scenario对象 dc_course_puppet
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@TableName("DC_COURSE_PUPPET")
public class DcCoursePuppet implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(value = "PUPPET_ID", type = IdType.ASSIGN_ID)
    @Excel(name = "主键")
    private String puppetId;

    /** 描述马甲 */
    @TableField("PUPPET_DESC")
    @Excel(name = "描述马甲")
    private String puppetDesc;

    /** 马甲名称 */
    @TableField("PUPPET_NAME")
    @Excel(name = "马甲名称")
    private String puppetName;

    /** 马甲图标 */
    @TableField("PUPPET_ICON")
    @Excel(name = "马甲图标")
    private String puppetIcon;

    /** 小组id */
    @TableField("GROUP_ID")
    @Excel(name = "小组id")
    private String groupId;

    /** 小组id */
    @TableField("PUPPET_INDEX")
    @Excel(name = "index")
    private String puppetIndex;

    @TableField(exist = false)
    private List<DcCourseStudent> ListDcCourseStudent;
}
