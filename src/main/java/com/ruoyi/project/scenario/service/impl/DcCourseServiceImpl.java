package com.ruoyi.project.scenario.service.impl;

import java.security.SecureRandom;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Random;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.project.scenario.domain.*;
import com.ruoyi.project.scenario.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.scenario.service.IDcCourseService;

/**
 * 场景课程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class DcCourseServiceImpl extends ServiceImpl<DcCourseMapper, DcCourse> implements IDcCourseService
{
    @Autowired
    private DcCourseMapper dcCourseMapper;

    @Autowired
    private DcSceneGroupMapper dcSceneGroupMapper;

    @Autowired
    private DcCourseGroupMapper dcCourseGroupMapper;

    @Autowired
    private DcSceneStageMapper dcSceneStageMapper;

    @Autowired
    private DcCourseStageMapper dcCourseStageMapper;

    @Autowired
    private DcScenePuppetMapper dcScenePuppetMapper;

    @Autowired
    private DcCoursePuppetMapper dcCoursePuppetMapper;

    @Autowired
    private DcSceneTestpaperMapper dcSceneTestpaperMapper;

    @Autowired
    private DcCourseTestpaperMapper dcCourseTestpaperMapper;

    @Autowired
    private DcSceneQuestionMapper dcSceneQuestionMapper;

    @Autowired
    private DcCourseQuestionMapper dcCourseQuestionMapper;

    @Autowired
    private DcSceneQuestionItemMapper dcSceneQuestionItemMapper;

    @Autowired
    private DcCourseQuestionItemMapper dcCourseQuestionItemMapper;

    public int add(DcCourse dcCourse) {
        String randomString = "";
        boolean randomStringIsExist = false;
        do {
            randomString = generateSecureRandomString(5,"number");
            Map<String,Object> randomStringMap = dcCourseMapper.checkRandomString(randomString);
            if(!"0".equals(randomStringMap.get("COURSE_CODE_COUNT")+"")){
                randomStringIsExist = true;
            }
        } while (randomStringIsExist); // 当 count 小于或等于5时，循环继续

        String courseId = UUID.fastUUID().toString();
        dcCourse.setCourseCode(randomString);
        dcCourse.setIsDelete(0);
        dcCourse.setCourseId(courseId);
        int insetInt = dcCourseMapper.insert(dcCourse);
        if(insetInt == 1){//新增成功
            //复制group表
            LambdaQueryWrapper<DcSceneGroup> querySceneGroupWrapper = new LambdaQueryWrapper<>();
            querySceneGroupWrapper.eq(DcSceneGroup::getSceneId,dcCourse.getSceneId());
            List<DcSceneGroup> dcSceneGroupList = dcSceneGroupMapper.selectList(querySceneGroupWrapper);
            for(DcSceneGroup dcSceneGroup : dcSceneGroupList){
                DcCourseGroup dcCourseGroup = getDcCourseGroup(dcCourse, dcSceneGroup,courseId);
                dcCourseGroupMapper.insert(dcCourseGroup);
                //复制小组里的马甲马甲
                LambdaQueryWrapper<DcScenePuppet> queryScenePuppetWrapper = new LambdaQueryWrapper<>();
                queryScenePuppetWrapper.eq(DcScenePuppet::getGroupId,dcSceneGroup.getGroupId());
                List<DcScenePuppet> dcScenePuppetList = dcScenePuppetMapper.selectList(queryScenePuppetWrapper);
                for(DcScenePuppet dcScenePuppet : dcScenePuppetList){
                    DcCoursePuppet dcCoursePuppet = new DcCoursePuppet();
                    dcCoursePuppet.setPuppetId(UUID.fastUUID().toString());
                    dcCoursePuppet.setPuppetDesc(dcScenePuppet.getPuppetDesc());
                    dcCoursePuppet.setPuppetIcon(dcScenePuppet.getPuppetIcon());
                    dcCoursePuppet.setPuppetName(dcScenePuppet.getPuppetName());
                    dcCoursePuppet.setGroupId(dcCourseGroup.getGroupId());
                    dcCoursePuppetMapper.insert(dcCoursePuppet);
                }
            }
            //复制阶段表
            LambdaQueryWrapper<DcSceneStage> querySceneStageWrapper = new LambdaQueryWrapper<>();
            querySceneStageWrapper.eq(DcSceneStage::getSceneId,dcCourse.getSceneId());
            List<DcSceneStage> dcSceneStageList = dcSceneStageMapper.selectList(querySceneStageWrapper);
            for( DcSceneStage dcSceneStage : dcSceneStageList){
                DcCourseStage dcCourseStage = getDcCourseStage(dcCourse, dcSceneStage, courseId);
                dcCourseStageMapper.insert(dcCourseStage);
            }
            //复制问卷
            LambdaQueryWrapper<DcSceneTestpaper> querySceneTestpaperWrapper = new LambdaQueryWrapper<>();
            querySceneTestpaperWrapper.eq(DcSceneTestpaper::getSceneId,dcCourse.getSceneId());
            List<DcSceneTestpaper> dcSceneTestpaperList = dcSceneTestpaperMapper.selectList(querySceneTestpaperWrapper);
            for(DcSceneTestpaper dcSceneTestpaper: dcSceneTestpaperList ){
                //复制问卷
                DcCourseTestpaper dcCourseTestpaper = new DcCourseTestpaper();
                dcCourseTestpaper.setPaperId(UUID.fastUUID().toString());
                dcCourseTestpaper.setPaperTitle(dcSceneTestpaper.getPaperTitle());
                dcCourseTestpaper.setSceneId(dcSceneTestpaper.getSceneId());
                dcCourseTestpaper.setCreateUser(dcSceneTestpaper.getCreateUser());
                dcCourseTestpaper.setCreateTime(dcSceneTestpaper.getCreateTime());
                dcCourseTestpaperMapper.insert(dcCourseTestpaper);


                LambdaQueryWrapper<DcSceneQuestion> querySceneQuestionWrapper = new LambdaQueryWrapper<>();
                querySceneQuestionWrapper.eq(DcSceneQuestion::getPaperId,dcSceneTestpaper.getPaperId());
                List<DcSceneQuestion> dcSceneQuestionList = dcSceneQuestionMapper.selectList(querySceneQuestionWrapper);
                for(DcSceneQuestion dcSceneQuestion : dcSceneQuestionList){
                    //复制题目
                    DcCourseQuestion dcCourseQuestion = new DcCourseQuestion();
                    dcCourseQuestion.setQuestionId(UUID.fastUUID().toString());
                    dcCourseQuestion.setQuestonText(dcSceneQuestion.getQuestonText());
                    dcCourseQuestion.setQuestionType(dcSceneQuestion.getQuestionType());
                    dcCourseQuestion.setPaperId(dcCourseTestpaper.getPaperId());
                    dcCourseQuestion.setQuestionAnswer(dcSceneQuestion.getQuestionAnswer());
                    dcCourseQuestion.setQuestionPoint(dcSceneQuestion.getQuestionPoint());
                    dcCourseQuestion.setQuestionIndex(dcSceneQuestion.getQuestionIndex());
                    dcCourseQuestion.setCreateUser(dcSceneQuestion.getCreateUser());
                    dcCourseQuestion.setCreateTime(dcSceneQuestion.getCreateTime());
                    dcCourseQuestionMapper.insert(dcCourseQuestion);

                    LambdaQueryWrapper<DcSceneQuestionItem> querySceneQuestionItemWrapper = new LambdaQueryWrapper<>();
                    querySceneQuestionItemWrapper.eq(DcSceneQuestionItem::getQuestionId,dcSceneQuestion.getQuestionId());
                    List<DcSceneQuestionItem> dcSceneQuestionItemList =  dcSceneQuestionItemMapper.selectList(querySceneQuestionItemWrapper);
                    for(DcSceneQuestionItem dcSceneQuestionItem : dcSceneQuestionItemList){
                        //复制内容
                        DcCourseQuestionItem dcCourseQuestionItem = new DcCourseQuestionItem();
                        dcCourseQuestionItem.setItemId(UUID.fastUUID().toString());
                        dcCourseQuestionItem.setQuestionId(dcCourseQuestion.getQuestionId());
                        dcCourseQuestionItem.setItemText(dcSceneQuestionItem.getItemText());
                        dcCourseQuestionItem.setIsRight(dcSceneQuestionItem.getIsRight());
                        dcCourseQuestionItem.setItemIndex(dcSceneQuestionItem.getItemIndex());
                        dcCourseQuestionItem.setCreateUser(dcSceneQuestionItem.getCreateUser());
                        dcCourseQuestionItem.setCreateUser(dcSceneQuestionItem.getCreateUser());
                        dcCourseQuestionItemMapper.insert(dcCourseQuestionItem);
                    }
                }

            }
        }
        return insetInt;
    }

    private static DcCourseStage getDcCourseStage(DcCourse dcCourse, DcSceneStage dcSceneStage, String courseId) {
        DcCourseStage dcCourseStage = new DcCourseStage();
        dcCourseStage.setCourseStageId(UUID.fastUUID().toString());
        dcCourseStage.setCourseId(courseId);
        dcCourseStage.setCourseStageTitle(dcSceneStage.getSceneStageTitle());
        dcCourseStage.setCourseStageText(dcSceneStage.getSceneStageText());
        dcCourseStage.setCourseStageOrder(dcSceneStage.getSceneStageOrder());
        dcCourseStage.setCreateUser(dcCourse.getCreateUser());
        dcCourseStage.setCreateTime(DateUtils.getNowDate());
        return dcCourseStage;
    }

    private static DcCourseGroup getDcCourseGroup(DcCourse dcCourse, DcSceneGroup dcSceneGroup, String courseId) {
        DcCourseGroup dcCourseGroup = new DcCourseGroup();
        dcCourseGroup.setGroupId(UUID.fastUUID().toString());
        dcCourseGroup.setCourseId(courseId);
        dcCourseGroup.setGroupName(dcSceneGroup.getGroupName());
        dcCourseGroup.setGroupNum(dcSceneGroup.getGroupNum());
        dcCourseGroup.setGroupOrder(dcSceneGroup.getGroupOrder());
        dcCourseGroup.setGroupIcon(dcSceneGroup.getGroupIcon());
        dcCourseGroup.setCreateUser(dcCourse.getCreateUser());
        dcCourseGroup.setCreateTime(DateUtils.getNowDate());
        return dcCourseGroup;
    }

    public static String generateSecureRandomString(int length,String type) {

        if("number".equals(type)){
            // 创建一个Random实例
            Random rand = new Random();
            // 生成一个5位的随机数并格式化为字符串
            return String.format("%05d", rand.nextInt(90000) + 10000);

        }else if("string".equals(type)){
            String upperCaseLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            String numbers = "0123456789";
            String combinedChars = upperCaseLetters + numbers;
            SecureRandom random = new SecureRandom();
            StringBuilder sb = new StringBuilder(length);
            for (int i = 0; i < length; i++) {
                int index = random.nextInt(combinedChars.length());
                sb.append(combinedChars.charAt(index));
            }
            return sb.toString();
        }else{
            return "";
        }
    }

    @Override
    public List<Map<String, Object>> selectGetTagsByUser(Map<String, Object> map) {
        return dcCourseMapper.selectGetTagsByUser(map);
    }

    @Override
    public List<DcCourse> selectDcCourseList(Map<String, Object> getCourseMap) {
        return dcCourseMapper.selectDcCourseList(getCourseMap);
    }

    @Override
    public List<DcCourse> getClassList() {
        return dcCourseMapper.getClassList();
    }

    @Override
    public DcCourse selectDcCourseByCourseId(String courseId) {
        return dcCourseMapper.selectDcCourseByCourseId(courseId);
    }
}
