package com.ruoyi.project.scenario.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.project.scenario.domain.DcCourseStudent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.scenario.mapper.DcCoursePaperMapper;
import com.ruoyi.project.scenario.domain.DcCoursePaperHistoryAll;
import com.ruoyi.project.scenario.service.IDcCoursePaperService;

import java.util.List;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Service
public class DcCoursePaperServiceImpl extends ServiceImpl<DcCoursePaperMapper, DcCoursePaperHistoryAll> implements IDcCoursePaperService
{

    @Autowired
    private DcCoursePaperMapper dcCoursePaperMapper;

    @Override
    public List<DcCourseStudent> selectStudentList(String courseId) {
        return dcCoursePaperMapper.selectStudentList(courseId);
    }



}
