package com.ruoyi.project.scenario.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.project.scenario.domain.*;
import com.ruoyi.project.scenario.mapper.DcCourseMapper;
import com.ruoyi.project.scenario.mapper.DcCoursePuppetMapper;
import com.ruoyi.project.scenario.mapper.DcCourseStudentMapper;
import com.ruoyi.project.student.domain.StSysUser;
import com.ruoyi.project.system.domain.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.scenario.mapper.DcCourseGroupMapper;
import com.ruoyi.project.scenario.service.IDcCourseGroupService;

/**
 * 课程分组Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-21
 */
@Service
public class DcCourseGroupServiceImpl extends ServiceImpl<DcCourseGroupMapper,DcCourseGroup> implements IDcCourseGroupService
{
    @Autowired
    private DcCourseGroupMapper dcCourseGroupMapper;
    @Autowired
    private DcCoursePuppetMapper dcCoursePuppetMapper;
    @Autowired
    private DcCourseMapper dcCourseMapper;
    @Autowired
    DcCourseStudentMapper dcCourseStudentMapper;
    @Override
    public List<Map<String, Object>> getTeGroupList(String courseId) {
        DcCourse DcCourse = dcCourseMapper.selectById(courseId);
        List<Map<String, Object>> list = dcCourseGroupMapper.getTeGroupList(courseId);
        for (Map<String, Object> map : list) {
            Map<String, Object> getMap = new HashMap<>(2);
            getMap.put("classCode", DcCourse.getClassCode());
            getMap.put("groupId", map.get("RESOURCE_ID"));
            List<Map<String, Object>> studentList = dcCourseGroupMapper.getStudentListByGroup(getMap);
//            if ("-1".equals(map.get("RESOURCE_ID") + "")) {
                map.put("USER_COUNT", studentList.size());
//            }
            map.put("STUDENT_LIST", studentList);
        }
        return list;
    }

    @Override
    public List<DcCourseGroup> getCourseGroupList(String courseId) {
        LambdaQueryWrapper<DcCourseGroup> queryCourseWrapper = new LambdaQueryWrapper<>();
        queryCourseWrapper.eq(DcCourseGroup::getCourseId,courseId);
        queryCourseWrapper.orderByAsc(DcCourseGroup::getGroupOrder);
        List<DcCourseGroup> list = dcCourseGroupMapper.selectList(queryCourseWrapper);
        for (DcCourseGroup dcCourseGroup : list) {
            LambdaQueryWrapper<DcCoursePuppet> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DcCoursePuppet::getGroupId, dcCourseGroup.getGroupId());
            queryWrapper.orderByAsc(DcCoursePuppet::getPuppetIndex);
            List<DcCoursePuppet> dcCoursePuppetList = dcCoursePuppetMapper.selectList(queryWrapper);
            for(DcCoursePuppet coursePuppet : dcCoursePuppetList){
                List<DcCourseStudent> listStudent = dcCourseStudentMapper.getStudentList(coursePuppet.getPuppetId());
                coursePuppet.setListDcCourseStudent(listStudent);
            }
            dcCourseGroup.setListCoursePuppet(dcCoursePuppetList);
        }
        return list;
    }

    @Override
    public List<Map<String, Object>> getListByCourseId(String courseId) {
        return dcCourseGroupMapper.getListByCourseId(courseId);
    }
}
