package com.ruoyi.project.scenario.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.project.scenario.domain.DcScene;
import com.ruoyi.project.scenario.domain.DcSceneTag;
import com.ruoyi.project.scenario.mapper.DcCourseGroupMapper;
import com.ruoyi.project.scenario.mapper.DcSceneMapper;
import com.ruoyi.project.scenario.service.IDcSceneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 场景Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-05
 */
@Service
public class DcSceneServiceImpl extends ServiceImpl<DcSceneMapper, DcScene> implements IDcSceneService
{
    @Autowired
    private DcSceneMapper dcSceneMapper;
    @Override
    public List<DcSceneTag> selectGetTagsByUser(Map<String, Object> map) {
        return dcSceneMapper.selectGetTagsByUser(map);
    }

    @Override
    public int addTag(Map<String, Object> map) {
        return dcSceneMapper.addTag(map);
    }

    @Override
    public int updateTag(Map<String, Object> map) {
        return dcSceneMapper.updateTag(map);
    }

    @Override
    public int delTag(String tagid) {
        return dcSceneMapper.delTag(tagid);
    }
}
