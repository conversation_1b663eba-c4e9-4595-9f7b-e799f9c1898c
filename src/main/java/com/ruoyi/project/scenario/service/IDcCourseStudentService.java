package com.ruoyi.project.scenario.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.project.scenario.domain.DcCourseStudent;

import java.util.List;
import java.util.Map;

/**
 * 课程内的学生Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface IDcCourseStudentService extends IService<DcCourseStudent> {

    public List<Map<String, Object>> listByPuppetId(String puppetId);
}
