package com.ruoyi.project.scenario.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.project.scenario.service.IDcCourseStudentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.scenario.mapper.DcCourseStudentMapper;
import com.ruoyi.project.scenario.domain.DcCourseStudent;

import java.util.List;
import java.util.Map;

/**
 * 课程内的学生Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@Service
public class DcCourseStudentServiceImpl extends ServiceImpl<DcCourseStudentMapper, DcCourseStudent> implements IDcCourseStudentService {

    @Autowired
    private DcCourseStudentMapper dcCourseStudentMapper;

    @Override
    public List<Map<String, Object>> listByPuppetId(String puppetId) {
        return dcCourseStudentMapper.listByPuppetId(puppetId);
    }
}
