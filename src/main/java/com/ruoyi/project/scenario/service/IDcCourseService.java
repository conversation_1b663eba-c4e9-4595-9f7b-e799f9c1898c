package com.ruoyi.project.scenario.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.project.scenario.domain.DcCourse;

/**
 * 场景课程Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface IDcCourseService  extends IService<DcCourse>
{

    public List<Map<String, Object>> selectGetTagsByUser(Map<String, Object> map);

    public List<DcCourse> selectDcCourseList(Map<String, Object> getCourseMap);

    public List<DcCourse> getClassList();

    public DcCourse selectDcCourseByCourseId(String courseId);

    public int add(DcCourse dcCourse);

}
