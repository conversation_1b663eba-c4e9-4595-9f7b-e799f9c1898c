package com.ruoyi.project.scenario.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.project.scenario.domain.DcScene;
import com.ruoyi.project.scenario.mapper.DcSceneMapper;
import com.ruoyi.project.scenario.service.IDcSceneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.scenario.mapper.DcSceneTagMapper;
import com.ruoyi.project.scenario.domain.DcSceneTag;
import com.ruoyi.project.scenario.service.IDcSceneTagService;

/**
 * 场景分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
public class DcSceneTagServiceImpl  extends ServiceImpl<DcSceneTagMapper, DcSceneTag> implements IDcSceneTagService
{

}
