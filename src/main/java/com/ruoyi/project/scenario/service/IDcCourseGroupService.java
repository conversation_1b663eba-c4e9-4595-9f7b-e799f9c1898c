package com.ruoyi.project.scenario.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.project.scenario.domain.DcCourseGroup;
import com.ruoyi.project.scenario.domain.DcSceneGroup;

/**
 * 课程分组Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-21
 */
public interface IDcCourseGroupService extends IService<DcCourseGroup>
{
    public List<Map<String, Object>> getTeGroupList(String courseId);

    public List<DcCourseGroup> getCourseGroupList(String courseId);

    public List<Map<String, Object>> getListByCourseId(String courseId);
}
