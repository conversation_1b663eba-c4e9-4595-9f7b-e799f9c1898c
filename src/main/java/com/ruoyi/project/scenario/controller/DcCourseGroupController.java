package com.ruoyi.project.scenario.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.project.scenario.domain.DcCoursePuppet;
import com.ruoyi.project.scenario.domain.DcSceneGroup;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.scenario.domain.DcCourseGroup;
import com.ruoyi.project.scenario.service.IDcCourseGroupService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 课程分组Controller
 * 
 * <AUTHOR>
 * @date 2025-06-21
 */
@RestController
@RequestMapping("/scenario/course/group")
public class DcCourseGroupController extends BaseController
{
    @Autowired
    private IDcCourseGroupService dcCourseGroupService;

    /**
     * 查询班级分组
     */
    @GetMapping("/te-group-list/{courseId}")
    public AjaxResult getTeGroupList(@PathVariable("courseId") String courseId)
    {
        return success(dcCourseGroupService.getTeGroupList(courseId));
    }

    /**
     * 查询场景课程分组
     */
    @GetMapping("/list/{courseId}")
    public AjaxResult list(@PathVariable("courseId") String courseId)
    {
        List<DcCourseGroup> list = dcCourseGroupService.getCourseGroupList(courseId);
        AjaxResult ajax = new AjaxResult();
        ajax.put("data",list);
        return ajax;
    }

    @GetMapping(value = "/{groupId}")
    public AjaxResult getInfo(@PathVariable("groupId") String groupId)
    {
        return success(dcCourseGroupService.getById(groupId));
    }

    /**
     * 新增课程分组
     */
    @Log(title = "课程分组", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DcCourseGroup dcCourseGroup)
    {
        dcCourseGroup.setCreateUser(getUserId().toString());
        return toAjax(dcCourseGroupService.save(dcCourseGroup));
    }

    /**
     * 修改课程分组
     */
    @Log(title = "课程分组", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DcCourseGroup dcCourseGroup)
    {
        return toAjax(dcCourseGroupService.updateById(dcCourseGroup));
    }

    /**
     * 删除课程分组
     */
    @Log(title = "课程分组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{groupId}")
    public AjaxResult remove(@PathVariable String groupId)
    {
        return toAjax(dcCourseGroupService.removeById(groupId));
    }



}
