package com.ruoyi.project.scenario.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.project.scenario.domain.DcSceneQuestionItem;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.scenario.domain.DcCourseStage;
import com.ruoyi.project.scenario.service.IDcCourseStageService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 课程阶段Controller
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@RestController
@RequestMapping("/scenario/course/stage")
public class DcCourseStageController extends BaseController
{
    @Autowired
    private IDcCourseStageService dcCourseStageService;

    /**
     * 查询课程阶段列表
     */
    @GetMapping("/list")
    public AjaxResult list(DcCourseStage dcCourseStage)
    {
        LambdaQueryWrapper<DcCourseStage> queryCourseStageWrapper = new LambdaQueryWrapper<>();
        queryCourseStageWrapper.eq(DcCourseStage::getCourseId,dcCourseStage.getCourseId());
        List<DcCourseStage> list = dcCourseStageService.list(queryCourseStageWrapper);
        return success(list);
    }

    /**
     * 获取课程阶段详细信息
     */
    @PreAuthorize("@ss.hasPermi('scenario:stage:query')")
    @GetMapping(value = "/{courseStageId}")
    public AjaxResult getInfo(@PathVariable("courseStageId") String courseStageId)
    {
        return success(dcCourseStageService.getById(courseStageId));
    }

    /**
     * 新增课程阶段
     */
    @PreAuthorize("@ss.hasPermi('scenario:stage:add')")
    @Log(title = "课程阶段", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DcCourseStage dcCourseStage)
    {
        dcCourseStage.setCreateUser(getUserId().toString());
        dcCourseStage.setCreateTime(DateUtils.getNowDate());
        return toAjax(dcCourseStageService.save(dcCourseStage));
    }

    /**
     * 修改课程阶段
     */
    @PreAuthorize("@ss.hasPermi('scenario:stage:edit')")
    @Log(title = "课程阶段", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DcCourseStage dcCourseStage)
    {
        dcCourseStage.setModifyUser(getUserId().toString());
        return toAjax(dcCourseStageService.updateById(dcCourseStage));
    }

    /**
     * 删除课程阶段
     */
    @PreAuthorize("@ss.hasPermi('scenario:stage:remove')")
    @Log(title = "课程阶段", businessType = BusinessType.DELETE)
	@DeleteMapping("/{courseStageId}")
    public AjaxResult remove(@PathVariable String courseStageId)
    {
        return toAjax(dcCourseStageService.removeById(courseStageId));
    }
}
