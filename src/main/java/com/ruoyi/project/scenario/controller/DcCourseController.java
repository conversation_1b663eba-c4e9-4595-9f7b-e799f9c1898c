package com.ruoyi.project.scenario.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.project.scenario.service.IDcSceneService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.scenario.domain.DcCourse;
import com.ruoyi.project.scenario.service.IDcCourseService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 场景课程Controller
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@RestController
@RequestMapping("/scenario/course")
public class DcCourseController extends BaseController
{
    @Autowired
    private IDcCourseService dcCourseService;
    @Autowired
    private IDcSceneService dcSceneService;

    /**
     * 查询场景课程列表
     */
    @GetMapping("/list")
    public AjaxResult list()
    {
        Map<String,Object> map = new HashMap<>();
        map.put("userId",getUserId());
        List<Map<String,Object>> tagList = dcCourseService.selectGetTagsByUser(map);
        for(Map<String,Object> tag : tagList){
            Map<String,Object> getCourseMap = new HashMap<>();
            getCourseMap.put("userId",getUserId());
            getCourseMap.put("tagId",tag.get("TAG_ID"));
            List<DcCourse> coursesList = dcCourseService.selectDcCourseList(getCourseMap);
            tag.put("coursesList",coursesList);
        }
        return success(tagList);
    }

    @GetMapping("/class/list")
    public AjaxResult getClassList()
    {
        List<DcCourse> list = dcCourseService.getClassList();
        AjaxResult ajax = new AjaxResult();
        ajax.put("data",list);
        return ajax;
    }


    /**
     * 获取场景课程详细信息
     */
    @GetMapping(value = "/{courseId}")
    public AjaxResult getInfo(@PathVariable("courseId") String courseId)
    {
        return success(dcCourseService.selectDcCourseByCourseId(courseId));
    }

    /**
     * 新增场景课程
     */
    @Log(title = "场景课程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DcCourse dcCourse)
    {
        dcCourse.setCreateUser(getUserId().toString());
        dcCourse.setIsDelete(0);
        return toAjax(dcCourseService.add(dcCourse));
    }

    /**
     * 修改场景课程
     */
    @Log(title = "场景课程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DcCourse dcCourse)
    {
        return toAjax(dcCourseService.updateById(dcCourse));
    }

    /**
     * 删除场景课程
     */
    @Log(title = "场景课程", businessType = BusinessType.DELETE)
	@DeleteMapping("/{courseIds}")
    public AjaxResult remove(@PathVariable String courseIds)
    {
        return toAjax(dcCourseService.removeById(courseIds));
    }
}
