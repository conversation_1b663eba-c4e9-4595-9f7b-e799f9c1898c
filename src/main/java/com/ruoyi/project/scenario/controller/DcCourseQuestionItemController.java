package com.ruoyi.project.scenario.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.project.scenario.domain.DcCourseQuestion;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.scenario.domain.DcCourseQuestionItem;
import com.ruoyi.project.scenario.service.IDcCourseQuestionItemService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@RestController
@RequestMapping("/scenario/course/item")
public class DcCourseQuestionItemController extends BaseController
{
    @Autowired
    private IDcCourseQuestionItemService dcCourseQuestionItemService;

    /**
     * 查询【请填写功能名称】列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DcCourseQuestionItem dcCourseQuestionItem)
    {
        LambdaQueryWrapper<DcCourseQuestionItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcCourseQuestionItem::getQuestionId,dcCourseQuestionItem.getQuestionId());
        List<DcCourseQuestionItem> list = dcCourseQuestionItemService.list(queryWrapper);
        return getDataTable(list);
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @GetMapping(value = "/{itemId}")
    public AjaxResult getInfo(@PathVariable("itemId") String itemId)
    {
        return success(dcCourseQuestionItemService.getById(itemId));
    }

    /**
     * 新增【请填写功能名称】
     */
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DcCourseQuestionItem dcCourseQuestionItem)
    {
        return toAjax(dcCourseQuestionItemService.save(dcCourseQuestionItem));
    }

    /**
     * 修改【请填写功能名称】
     */
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DcCourseQuestionItem dcCourseQuestionItem)
    {
        return toAjax(dcCourseQuestionItemService.updateById(dcCourseQuestionItem));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('scenario:item:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{itemIds}")
    public AjaxResult remove(@PathVariable String itemIds)
    {
        return toAjax(dcCourseQuestionItemService.removeById(itemIds));
    }
}
