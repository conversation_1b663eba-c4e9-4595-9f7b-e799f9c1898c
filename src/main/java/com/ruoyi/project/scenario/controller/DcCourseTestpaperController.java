package com.ruoyi.project.scenario.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.project.scenario.domain.DcCourseQuestion;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.scenario.domain.DcCourseTestpaper;
import com.ruoyi.project.scenario.service.IDcCourseTestpaperService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@RestController
@RequestMapping("/scenario/course/testpaper")
public class DcCourseTestpaperController extends BaseController
{
    @Autowired
    private IDcCourseTestpaperService dcCourseTestpaperService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('scenario:testpaper:list')")
    @GetMapping("/list")
    public AjaxResult list(DcCourseTestpaper dcCourseTestpaper)
    {
        LambdaQueryWrapper<DcCourseTestpaper> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcCourseTestpaper::getSceneId,dcCourseTestpaper.getSceneId());
        List<DcCourseTestpaper> list = dcCourseTestpaperService.list(queryWrapper);
        return success(list);
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('scenario:testpaper:query')")
    @GetMapping(value = "/{paperId}")
    public AjaxResult getInfo(@PathVariable("paperId") String paperId)
    {
        return success(dcCourseTestpaperService.getById(paperId));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('scenario:testpaper:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DcCourseTestpaper dcCourseTestpaper)
    {
        return toAjax(dcCourseTestpaperService.save(dcCourseTestpaper));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('scenario:testpaper:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DcCourseTestpaper dcCourseTestpaper)
    {
        return toAjax(dcCourseTestpaperService.updateById(dcCourseTestpaper));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('scenario:testpaper:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{paperIds}")
    public AjaxResult remove(@PathVariable String paperIds)
    {
        return toAjax(dcCourseTestpaperService.removeById(paperIds));
    }
}
