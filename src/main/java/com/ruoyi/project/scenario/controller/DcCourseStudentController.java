package com.ruoyi.project.scenario.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.project.scenario.mapper.DcCourseStudentMapper;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.scenario.domain.DcCourseStudent;
import com.ruoyi.project.scenario.service.IDcCourseStudentService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 课程内的学生Controller
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@RestController
@RequestMapping("/scenario/student")
public class DcCourseStudentController extends BaseController
{
    @Autowired
    private IDcCourseStudentService dcCourseStudentService;

    @Autowired
    private DcCourseStudentMapper dcCourseStudentMapper;

    /**
     * 查询课程内的学生列表
     */

    /**
     * 新增课程内的学生
     */
    @Log(title = "课程内的学生", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DcCourseStudent dcCourseStudent)
    {
        dcCourseStudentMapper.updatePlusIndex(dcCourseStudent);//新增位置后面的index+1
        return toAjax(dcCourseStudentService.save(dcCourseStudent));
    }

    /**
     * 修改课程内的学生
     */
    @Log(title = "课程内的学生", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DcCourseStudent dcCourseStudent)
    {
        DcCourseStudent dcCourseStudentOld = dcCourseStudentService.getById(dcCourseStudent.getStudentId());
        dcCourseStudentMapper.updateMinusIndex(dcCourseStudentOld);//移除的位置后面的index都-1
        dcCourseStudentMapper.updatePlusIndex(dcCourseStudent);//新增位置后面的index+1
        return toAjax(dcCourseStudentService.updateById(dcCourseStudent));
    }

    /**
     * 删除课程内的学生
     */
    @Log(title = "课程内的学生", businessType = BusinessType.DELETE)
	@DeleteMapping("/{studentId}")
    public AjaxResult remove(@PathVariable String studentId)
    {
        DcCourseStudent dcCourseStudent = dcCourseStudentService.getById(studentId);
        dcCourseStudentMapper.updateMinusIndex(dcCourseStudent);//移除的位置后面的index都-1
        return toAjax(dcCourseStudentService.removeById(studentId));
    }
}
