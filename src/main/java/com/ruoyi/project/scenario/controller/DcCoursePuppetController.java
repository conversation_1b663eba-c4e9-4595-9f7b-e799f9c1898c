package com.ruoyi.project.scenario.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.project.scenario.domain.DcCourse;
import com.ruoyi.project.scenario.domain.DcScenePuppet;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.scenario.domain.DcCoursePuppet;
import com.ruoyi.project.scenario.service.IDcCoursePuppetService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * scenarioController
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@RestController
@RequestMapping("/scenario/course/puppet")
public class DcCoursePuppetController extends BaseController
{
    @Autowired
    private IDcCoursePuppetService dcCoursePuppetService;

    /**
     * 新增场景内的马甲
     */
    @Log(title = "课程内的马甲", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DcCoursePuppet dcCoursePuppet)
    {
        return toAjax(dcCoursePuppetService.save(dcCoursePuppet));
    }

    @Log(title = "课程内的马甲", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DcCoursePuppet dcCoursePuppet)
    {
        return toAjax(dcCoursePuppetService.updateById(dcCoursePuppet));
    }

    @Log(title = "场景内的马甲", businessType = BusinessType.DELETE)
    @DeleteMapping("/{puppetId}")
    public AjaxResult remove(@PathVariable String puppetId)
    {
        return toAjax(dcCoursePuppetService.removeById(puppetId));
    }
    @GetMapping(value = "/{puppetId}")
    public AjaxResult getInfo(@PathVariable("puppetId") String puppetId)
    {
        return success(dcCoursePuppetService.getById(puppetId));
    }





}
