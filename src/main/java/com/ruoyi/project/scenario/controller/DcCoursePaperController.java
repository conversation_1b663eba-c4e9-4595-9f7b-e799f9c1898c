package com.ruoyi.project.scenario.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.scenario.domain.DcCoursePuppet;
import com.ruoyi.project.scenario.service.IDcCourseGroupService;
import com.ruoyi.project.scenario.service.IDcCoursePuppetService;
import com.ruoyi.project.scenario.service.IDcCourseStudentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@RestController
@RequestMapping("/scenario/course/paper")
public class DcCoursePaperController extends BaseController
{
    @Autowired
    private IDcCourseGroupService dcCourseGroupService;
    @Autowired
    private IDcCourseStudentService dcCourseStudentService;
    @Autowired
    private IDcCoursePuppetService dcCoursePuppetService;
    /**
     * 查询组或者人tree
     */
    @GetMapping("/tree/{type}/{courseId}")
    public AjaxResult list(@PathVariable("type") String type,@PathVariable("courseId") String courseId)
    {
        List<Map<String,Object>> groups = dcCourseGroupService.getListByCourseId(courseId);
        if("1".equals(type)){
            for(Map<String, Object> group : groups){
                List<Map<String, Object> > studentList = new ArrayList<>();
                LambdaQueryWrapper<DcCoursePuppet> queryPuppetWrapper = new LambdaQueryWrapper<>();
                queryPuppetWrapper.eq(DcCoursePuppet::getGroupId,group.get("ID"));
                List<DcCoursePuppet> puppets = dcCoursePuppetService.list(queryPuppetWrapper);
                for(DcCoursePuppet puppet : puppets){
                    List<Map<String, Object> > students = dcCourseStudentService.listByPuppetId(puppet.getPuppetId());
                    studentList.addAll(students);
                }
                group.put("studentList",studentList);
            }
        }
        return success(groups);
    }
}
