package com.ruoyi.project.scenario.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.scenario.domain.DcScene;
import com.ruoyi.project.scenario.domain.DcScenePuppet;
import com.ruoyi.project.scenario.domain.DcSceneTag;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 场景Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-05
 */
@Mapper
public interface DcSceneMapper extends BaseMapper<DcScene>
{

    public List<DcSceneTag> selectGetTagsByUser(Map<String, Object> map);

    public int addTag(Map<String, Object> map);

    public int updateTag(Map<String, Object> map);

    public int delTag(String tagid);
}
