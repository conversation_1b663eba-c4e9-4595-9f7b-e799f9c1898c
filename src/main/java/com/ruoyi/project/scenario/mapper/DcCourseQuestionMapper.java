package com.ruoyi.project.scenario.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.scenario.domain.DcCourseQuestion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 课程试题Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@Mapper
public interface DcCourseQuestionMapper extends BaseMapper<DcCourseQuestion>
{
    /**
     * 获取上一题
     * @param questionId 当前题目ID
     * @param paperId 问卷ID
     * @return 上一题
     */
    DcCourseQuestion getLagQuestion(@Param("questionId") String questionId, @Param("paperId") String paperId);

    /**
     * 获取下一题
     * @param questionId 当前题目ID
     * @param paperId 问卷ID
     * @return 下一题
     */
    DcCourseQuestion getLeadQuestion(@Param("questionId") String questionId, @Param("paperId") String paperId);
}
