package com.ruoyi.project.common;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.http.ResponseEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ruoyi.framework.aspectj.lang.annotation.Anonymous;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.redis.RedisCache;

import java.util.HashMap;
import java.util.Map;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * 系统健康检查控制器
 * 提供系统状态检查和监控功能
 * 
 * <AUTHOR>
 */
@Anonymous
@RestController
@RequestMapping("/health")
public class HealthController {
    
    private static final Logger log = LoggerFactory.getLogger(HealthController.class);
    
    @Autowired(required = false)
    private RedisCache redisCache;
    
    /**
     * 系统健康检查接口
     * 检查数据库连接、Redis连接等基础服务状态
     * 
     * @return 健康检查结果
     */
    @GetMapping("/check")
    public AjaxResult healthCheck() {
        long startTime = System.currentTimeMillis();
        
        try {
            log.debug("[HealthCheck] 开始系统健康检查");
            
            Map<String, Object> healthData = new HashMap<>();
            
            // 基础信息
            healthData.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            healthData.put("status", "UP");
            healthData.put("service", "情景课堂后端服务");
            
            // 检查Redis连接（如果配置了Redis）
            boolean redisStatus = checkRedisHealth();
            healthData.put("redis", redisStatus ? "UP" : "DOWN");
            
            // 检查服务响应时间
            long responseTime = System.currentTimeMillis() - startTime;
            healthData.put("responseTime", responseTime + "ms");
            
            // 内存使用情况
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            Map<String, Object> memoryInfo = new HashMap<>();
            memoryInfo.put("total", formatBytes(totalMemory));
            memoryInfo.put("free", formatBytes(freeMemory));
            memoryInfo.put("used", formatBytes(usedMemory));
            memoryInfo.put("usagePercent", String.format("%.2f%%", (double)usedMemory / totalMemory * 100));
            
            healthData.put("memory", memoryInfo);
            
            // 系统负载情况（简单检查）
            String loadStatus = "NORMAL";
            if (usedMemory > totalMemory * 0.9) {
                loadStatus = "HIGH";
            } else if (usedMemory > totalMemory * 0.7) {
                loadStatus = "MEDIUM";
            }
            healthData.put("load", loadStatus);
            
            log.info("[HealthCheck] 系统健康检查完成，状态：正常，响应时间：{}ms", responseTime);
            
            return AjaxResult.success("系统运行正常", healthData);
            
        } catch (Exception e) {
            log.error("[HealthCheck] 系统健康检查异常", e);
            
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            errorData.put("status", "DOWN");
            errorData.put("service", "情景课堂后端服务");
            errorData.put("error", e.getMessage());
            errorData.put("responseTime", (System.currentTimeMillis() - startTime) + "ms");
            
            AjaxResult result = AjaxResult.error(500, "系统健康检查失败");
            result.put("data", errorData);
            return result;
        }
    }
    
    /**
     * 简单的状态检查接口（更轻量级）
     * 
     * @return 简单的状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> simpleStatusCheck() {
        Map<String, Object> status = new HashMap<>();
        status.put("status", "UP");
        status.put("timestamp", System.currentTimeMillis());
        status.put("service", "情景课堂后端服务");
        
        return ResponseEntity.ok(status);
    }
    
    /**
     * 检查Redis连接状态
     * 
     * @return Redis连接是否正常
     */
    private boolean checkRedisHealth() {
        if (redisCache == null) {
            log.debug("[HealthCheck] Redis未配置，跳过Redis健康检查");
            return true; // 如果没有配置Redis，认为是正常的
        }
        
        try {
            // 尝试执行简单的Redis操作
            String testKey = "health_check_test";
            redisCache.setCacheObject(testKey, "test_value", 10, TimeUnit.SECONDS); // 10秒过期
            String testValue = redisCache.getCacheObject(testKey);
            redisCache.deleteObject(testKey); // 清理测试数据
            
            boolean isHealthy = "test_value".equals(testValue);
            log.debug("[HealthCheck] Redis健康检查结果：{}", isHealthy ? "正常" : "异常");
            
            return isHealthy;
        } catch (Exception e) {
            log.warn("[HealthCheck] Redis健康检查异常：{}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 格式化字节数为可读的大小
     * 
     * @param bytes 字节数
     * @return 格式化后的字符串
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) {
            return bytes + "B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2fKB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2fMB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.2fGB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
} 