let idCounter = 0;
let idPrefix = 'click-id'; // 移到全局作用域

/**
 * 生成唯一的ID
 */
function generateId() {
  return `${idPrefix}-${Date.now()}-${++idCounter}`;
}

/**
 * 判断元素是否为可点击元素
 */
function isClickableElement(element) {
  try {
    // 常见的可点击标签
    const clickableTags = ['BUTTON', 'A', 'INPUT'];
    if (clickableTags.includes(element.tagName)) {
      return true;
    }
    
    // 检查是否有点击相关的属性或角色
    if (element.getAttribute('role') === 'button') {
      return true;
    }
    
    if (element.hasAttribute('onclick')) {
      return true;
    }
    
    // 检查CSS样式
    try {
      const style = window.getComputedStyle(element);
      if (style && style.cursor === 'pointer') {
        return true;
      }
    } catch (styleError) {
      // 某些元素可能无法获取计算样式，忽略此检查
    }
    
    // 检查类名是否包含按钮相关关键词
    // 处理SVG元素的className可能是SVGAnimatedString对象的情况
    let className = '';
    if (element.className) {
      if (typeof element.className === 'string') {
        className = element.className;
      } else if (element.className.baseVal) {
        // SVG元素的className是SVGAnimatedString对象
        className = element.className.baseVal;
      } else if (element.className.toString) {
        className = element.className.toString();
      }
    }
    
    const lowerClassName = className.toLowerCase();
    if (lowerClassName.includes('btn') || 
        lowerClassName.includes('button') || 
        lowerClassName.includes('clickable') ||
        lowerClassName.includes('el-button') ||
        lowerClassName.includes('el-link')) {
      return true;
    }
    
    // 检查是否有Vue事件监听器的痕迹 - 改进Vue3检测
    // Vue3中可能使用不同的属性名
    if (element.__vueParentComponent || 
        element._vueParentComponent || 
        element.__vue__ ||
        element._vnode) {
      // 检查是否有事件监听器 (getEventListeners只在Chrome DevTools中可用)
      try {
        if (typeof element.getEventListeners === 'function') {
          const listeners = element.getEventListeners();
          if (listeners && Object.keys(listeners).length > 0) {
            return true;
          }
        }
      } catch (listenerError) {
        // getEventListeners不可用，忽略此检查
      }
      
      // 对于Vue管理的元素，可以认为是可能可点击的
      // 但为了避免误判，只有在其他条件满足时才返回true
      // 这里可以根据实际需要调整策略
    }
    
    return false;
  } catch (error) {
    console.warn('[AutoClickId] Error checking clickable element:', error);
    return false;
  }
}

/**
 * 检查ID是否已存在于文档中
 */
function isIdUnique(id) {
  return !document.getElementById(id);
}

/**
 * 为容器内的可点击元素添加ID
 */
function addIdToClickElements(container = document.body) {
  if (!container || !container.querySelectorAll) {
    console.warn('[AutoClickId] Invalid container provided');
    return;
  }
  
  try {
    // 获取所有元素
    const allElements = container.querySelectorAll('*');
    
    allElements.forEach(element => {
      try {
        // 如果已经有id就跳过 - 加强检查
        if (element.id && element.id.trim() !== '') {
          return;
        }
        
        // 检查是否为可点击元素
        if (isClickableElement(element)) {
          let newId = generateId();
          let attempts = 0;
          const maxAttempts = 10;
          
          // 确保ID唯一性
          while (!isIdUnique(newId) && attempts < maxAttempts) {
            newId = generateId();
            attempts++;
          }
          
          if (attempts >= maxAttempts) {
            console.warn('[AutoClickId] Could not generate unique ID after', maxAttempts, 'attempts');
            return;
          }
          
          element.id = newId;
          
          // 在开发环境下输出日志
          if (process.env.NODE_ENV === 'development') {
            console.log(`[AutoClickId] Added ID "${newId}" to element:`, element);
          }
        }
      } catch (elementError) {
        console.warn('[AutoClickId] Error processing individual element:', elementError);
      }
    });
  } catch (error) {
    console.warn('[AutoClickId] Error processing elements:', error);
  }
}

/**
 * Vue3插件定义
 */
const AutoClickIdPlugin = {
  install(app, options = {}) {
    const config = {
      // 是否启用调试模式
      debug: process.env.NODE_ENV === 'development',
      // 是否启用MutationObserver
      enableObserver: true,
      // 自定义ID前缀
      idPrefix: 'click-id',
      // 延迟处理时间（毫秒）
      processDelay: 100,
      ...options
    };

    // 如果提供了自定义前缀，更新全局前缀
    if (config.idPrefix !== 'click-id') {
      idPrefix = config.idPrefix;
    }

    // 在每个组件挂载后扫描其DOM
    app.mixin({
      mounted() {
        this.$nextTick(() => {
          try {
            if (this.$el && this.$el.nodeType === Node.ELEMENT_NODE) {
              // 使用配置的延迟时间
              setTimeout(() => {
                addIdToClickElements(this.$el);
              }, config.processDelay);
            }
          } catch (error) {
            console.warn('[AutoClickId] Error in mounted hook:', error);
          }
        });
      },
      updated() {
        this.$nextTick(() => {
          try {
            if (this.$el && this.$el.nodeType === Node.ELEMENT_NODE) {
              // 使用配置的延迟时间
              setTimeout(() => {
                addIdToClickElements(this.$el);
              }, config.processDelay);
            }
          } catch (error) {
            console.warn('[AutoClickId] Error in updated hook:', error);
          }
        });
      }
    });

    // 在浏览器环境中设置MutationObserver
    if (typeof window !== 'undefined' && config.enableObserver) {
      let observer;
      let processTimeout;
      
      const startObserver = () => {
        if (observer) return;
        
        observer = new MutationObserver((mutations) => {
          try {
            // 清除之前的处理任务
            if (processTimeout) {
              clearTimeout(processTimeout);
            }
            
            // 批量处理变更，避免频繁处理
            processTimeout = setTimeout(() => {
              mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                  mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                      addIdToClickElements(node);
                    }
                  });
                }
              });
            }, config.processDelay);
          } catch (error) {
            console.warn('[AutoClickId] Error in MutationObserver:', error);
          }
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true
        });

        if (config.debug) {
          console.log('[AutoClickId] MutationObserver started');
        }
      };

      // 页面加载完成后开始观察
      const initializePlugin = () => {
        try {
          // 先扫描现有的DOM
          addIdToClickElements(document.body);
          
          // 启动观察器
          startObserver();
          
          if (config.debug) {
            console.log('[AutoClickId] Plugin initialized');
          }
        } catch (error) {
          console.error('[AutoClickId] Error initializing plugin:', error);
        }
      };

      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializePlugin);
      } else {
        // 延迟初始化，确保Vue应用已经挂载
        setTimeout(initializePlugin, config.processDelay);
      }
      
      // 提供全局方法，方便手动触发
      app.config.globalProperties.$addAutoIds = (container) => {
        try {
          addIdToClickElements(container || document.body);
        } catch (error) {
          console.warn('[AutoClickId] Error in manual trigger:', error);
        }
      };
      
      // 提供停止观察的方法
      app.config.globalProperties.$stopAutoIdObserver = () => {
        try {
          if (observer) {
            observer.disconnect();
            observer = null;
            if (config.debug) {
              console.log('[AutoClickId] Observer stopped');
            }
          }
          if (processTimeout) {
            clearTimeout(processTimeout);
            processTimeout = null;
          }
        } catch (error) {
          console.warn('[AutoClickId] Error stopping observer:', error);
        }
      };
    }

    if (config.debug) {
      console.log('[AutoClickId] Plugin installed with config:', config);
    }
  }
};

export default AutoClickIdPlugin; 