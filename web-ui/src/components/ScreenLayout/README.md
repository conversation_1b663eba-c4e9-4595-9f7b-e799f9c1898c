# ScreenLayout 通用屏幕布局组件

通用的屏幕布局组件，包含头部、左侧面板和右侧面板，支持全屏模式和各种自定义配置。

## 组件特性

- 📱 响应式设计，支持移动端适配
- 🔧 高度可配置的左右面板
- 📋 内置菜单列表和复选框功能
- 🎨 可自定义图标、标题和样式
- 🔌 丰富的插槽支持

## 基本用法

```vue
<template>
  <ScreenLayout
    :menu-items="groupList"
    :selected-items="selectedGroups"
    :show-checkbox="true"
    left-default-icon="Menu"
    left-default-title="群组列表"
    right-default-icon="HomeFilled"
    right-default-title="聊天窗口"
    :show-empty-state="displayedGroups.length === 0"
    empty-state-text="请在左侧勾选要监控的群组"
    item-key-field="groupId"
    item-name-field="groupName"
    menu-item-icon="User"
    @item-select="selectGroup"
    @item-toggle="handleGroupToggle"
    @fab-click="handleFabClick">
    
    <!-- 右侧头部额外内容 -->
    <template #right-header-extra>
      <WebSocketManager 
        :selected-groups="selectedGroups"
        @message-received="handleMessageReceived" />
    </template>
    
    <!-- 右侧内容区域 -->
    <template #right-content>
      <div class="chat-windows-grid">
        <div v-for="group in displayedGroups" :key="group.groupId">
          <GroupChatWindow :group-info="group" />
        </div>
      </div>
    </template>
  </ScreenLayout>
</template>
```

## Props 配置

### 基本配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| iconColor | String | 'rgba(189, 4, 7, 1)' | 图标颜色 |

### 左侧面板配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| leftPanelWidth | String | '280px' | 左侧面板宽度 |
| leftDefaultIcon | String | '' | 左侧面板默认图标 |
| leftDefaultTitle | String | '菜单列表' | 左侧面板默认标题 |

### 右侧面板配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| rightDefaultIcon | String | '' | 右侧面板默认图标 |
| rightDefaultTitle | String | '内容区域' | 右侧面板默认标题 |

### 菜单项配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| menuItems | Array | [] | 菜单项数组 |
| menuItemIcon | String | '' | 菜单项图标 |
| itemKeyField | String | 'id' | 菜单项的键值字段名 |
| itemNameField | String | 'name' | 菜单项的名称字段名 |

### 选择功能配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| showCheckbox | Boolean | false | 是否显示复选框 |
| selectedItems | Array | [] | 已选中的项目键值数组 |
| selectedItemKey | String/Number | '' | 当前选中的项目键值 |

### 空状态配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| showEmptyState | Boolean | false | 是否显示空状态 |
| emptyStateText | String | '暂无内容' | 空状态提示文本 |

## 插槽

| 插槽名 | 说明 | 作用域参数 |
|--------|------|-----------|
| left-header | 左侧面板头部自定义内容 | - |
| menu-item | 菜单项自定义内容 | { item, isSelected } |
| right-header | 右侧面板头部自定义内容 | - |
| right-header-extra | 右侧面板头部额外内容 | - |
| right-content | 右侧面板内容区域 | - |
| fab | 悬浮按钮自定义 | - |

## 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| item-select | 菜单项被选中时触发 | (item) |
| item-toggle | 菜单项复选框状态切换时触发 | (itemKey, checked) |
| fab-click | 悬浮按钮点击时触发 | (item) |

## 样式特性

- **左侧面板**：白色背景，可自定义宽度，统一的头部样式
- **右侧面板**：使用 flex 布局，内容区域带白色背景和圆角
- **菜单项**：支持hover效果（红色高亮）、选中状态和过渡动画
- **复选框**：自定义主题色样式（红色主题）
- **头部区域**：统一的 padding 和字体样式
- **空状态**：居中显示的友好提示界面
- **响应式**：移动端自动切换为垂直布局
- **自定义按钮**：统一的主题色按钮样式

## 使用场景

- 监控大屏页面
- 管理后台页面
- 聊天室界面
- 数据展示页面
- 任何需要左右分栏布局的页面

## 注意事项

1. 菜单项数据结构需要包含指定的键值字段和名称字段
2. 右侧内容区域具有固定的margin和padding，适合网格布局
3. 图标组件需要使用Element Plus的图标组件 