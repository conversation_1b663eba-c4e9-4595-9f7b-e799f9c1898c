<template>
  <div class="screen-header-bar">
    <div class="header-content">
      <div class="header-left">
        <img src="@/assets/images/teacher-logo.png" alt="Logo" class="header-logo" />
        <h1 class="header-title">{{ displayTitle }}</h1>
      </div>
      <div class="header-right">
          <img src="@/assets/images/teacher-header-right.png" alt="Header Right" class="header-right-image" />
        <!-- 用户信息下拉菜单 -->
        <div class="user-container">
          <el-dropdown @command="handleCommand" class="user-dropdown" trigger="click">
            <div class="user-wrapper">
              <img :src="userStore.avatar" class="user-avatar" />
              <span class="user-name">{{ userStore.name }}</span>
              <el-icon class="dropdown-icon"><caret-bottom /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <router-link to="/user/profile">
                  <el-dropdown-item>个人中心</el-dropdown-item>
                </router-link>
                <el-dropdown-item divided command="logout">
                  <span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      
      </div>
    </div>
  </div>
</template>

<script setup name="ScreenHeader">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { CaretBottom } from '@element-plus/icons-vue'
import { getConfigKeyV2 } from '@/api/system/config'
import useUserStore from '@/store/modules/user'

// 路由和用户store
const router = useRouter()
const userStore = useUserStore()

// 定义props
const props = defineProps({
  // 标题，如果没有传入则从配置中获取
  title: {
    type: String,
    default: ''
  },
  // 配置键名，用于从系统配置中获取标题
  configKey: {
    type: String,
    default: 'teacher.monitor.screen.title'
  }
})

// 响应式数据
const displayTitle = ref(props.title || '数智情景课堂')

// 获取页面标题配置
const fetchPageTitle = async () => {
  try {
    const response = await getConfigKeyV2(props.configKey)
    if (response.code === 200 && response.data) {
      displayTitle.value = response.data.value
    }
  } catch (error) {
    console.error('获取页面标题配置失败:', error)
    // 使用默认标题
  }
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case "logout":
      logout()
      break
    default:
      break
  }
}

// 退出登录
const logout = () => {
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logOut().then(() => {
      location.href = '/index'
    })
  }).catch(() => {})
}

// 生命周期
onMounted(() => {
  // 只有在没有传入title prop时才从配置获取
  if (!props.title) {
    fetchPageTitle()
  } else {
    displayTitle.value = props.title
  }
})
</script>

<style scoped>
/* 红色头部栏 */
.screen-header-bar {
  background: linear-gradient(90deg, rgba(189, 4, 7, 1) 0%, rgba(189, 4, 7, 0.8) 100%);
  color: white;
  padding: 0 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-logo {
  height: 30px;
  width: auto;
  margin-right: 15px;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
  margin: 0;
  color: white;
  height: 60px;
  line-height: 60px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

/* 用户信息样式 */
.user-container {
  display: flex;
  align-items: center;
}

.user-dropdown {
  cursor: pointer;
}

.user-wrapper {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
  color: white;
}

.user-wrapper:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  margin-right: 6px;
  color: white;
}

.dropdown-icon {
  font-size: 12px;
  color: white;
  transition: transform 0.3s;
}

.user-wrapper:hover .dropdown-icon {
  transform: scale(1.1);
}

.header-right-image {
  height: 60px;
  width: auto;
  margin: 0;
  vertical-align: top;
}

/* 下拉菜单自定义样式 */
:deep(.el-dropdown-menu) {
  min-width: 120px;
  border: 1px solid rgba(189, 4, 7, 0.2);
}

:deep(.el-dropdown-menu__item) {
  color: #333;
  transition: all 0.3s;
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: rgba(189, 4, 7, 0.1);
  color: rgba(189, 4, 7, 1);
}

:deep(.el-dropdown-menu__item a) {
  color: inherit;
  text-decoration: none;
  display: block;
  width: 100%;
  height: 100%;
}
</style> 