<template>
  <div class="question-chart-container">
    <div class="chart-header">
      <div class="question-info">
        <span class="question-number">第{{ questionOrder }}题</span>
        <span class="question-type-tag">
          {{ questionType === 1 ? '单选' : '多选' }}
        </span>
        <span class="chart-switch">
          <el-radio-group v-model="chartType" size="small" @change="renderChart">
            <el-radio-button value="bar">柱状图</el-radio-button>
            <el-radio-button value="pie">饼图</el-radio-button>
          </el-radio-group>
        </span>
      </div>
      <div class="question-text">{{ questionText }}</div>
      <div class="question-meta">
        <span class="answer-count">回答人数: {{ totalAnswers || 0 }}</span>
      </div>
    </div>
    
    <!-- 图表容器 -->
    <div ref="chartRef" class="chart-wrapper" :style="{ height: chartHeight }"></div>
    
    <!-- 选项统计详情 -->
    <div class="options-detail">
      <div 
        v-for="option in optionStatistics" 
        :key="option.itemId"
        class="option-detail-item">
        <div class="option-info">
          <span class="option-text">{{ option.itemText }}</span>
          <span class="option-stats">
            {{ option.selectCount || 0 }}人 ({{ option.selectRate || 0 }}%)
          </span>
        </div>
        <div class="option-progress">
          <el-progress 
            :percentage="option.selectRate || 0" 
            :stroke-width="6"
            :color="getProgressColor(option.selectRate)"
            :show-text="false">
          </el-progress>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="QuestionChart">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

// Props定义
const props = defineProps({
  questionId: {
    type: String,
    required: true
  },
  questionText: {
    type: String,
    required: true
  },
  questionType: {
    type: Number,
    required: true
  },
  questionOrder: {
    type: Number,
    required: true
  },
  optionStatistics: {
    type: Array,
    default: () => []
  },
  totalAnswers: {
    type: Number,
    default: 0
  },
  height: {
    type: String,
    default: '300px'
  }
})

// 响应式数据
const chartRef = ref(null)
const chartInstance = ref(null)
const chartType = ref('bar') // bar | pie
const chartHeight = ref(props.height)

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage >= 60) return '#67c23a'
  if (percentage >= 30) return '#e6a23c'
  return '#f56c6c'
}

// 获取图表颜色配置
const getChartColors = () => {
  return [
    '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
    '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#ff9f7f'
  ]
}

// 渲染柱状图
const renderBarChart = () => {
  if (!chartInstance.value || !props.optionStatistics?.length) return

  const xData = props.optionStatistics.map(item => {
    // 截断过长的选项文本
    const text = item.itemText || ''
    return text.length > 10 ? text.substring(0, 10) + '...' : text
  })
  const yData = props.optionStatistics.map(item => item.selectCount || 0)
  const percentages = props.optionStatistics.map(item => item.selectRate || 0)

  const option = {
    title: {
      show: false
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        const dataIndex = params[0].dataIndex
        const option = props.optionStatistics[dataIndex]
        return `${option.itemText}<br/>选择人数: ${option.selectCount || 0}<br/>选择率: ${option.selectRate || 0}%`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xData,
      axisTick: {
        alignWithLabel: true
      },
      axisLabel: {
        interval: 0,
        rotate: xData.some(item => item.length > 6) ? -45 : 0,
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      name: '选择人数',
      nameTextStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '选择人数',
        type: 'bar',
        barWidth: '60%',
        itemStyle: {
          color: function(params) {
            const colors = getChartColors()
            return colors[params.dataIndex % colors.length]
          },
          borderRadius: [4, 4, 0, 0]
        },
        label: {
          show: true,
          position: 'top',
          formatter: function(params) {
            const percentage = percentages[params.dataIndex]
            return `${params.value}\n(${percentage}%)`
          },
          fontSize: 11
        },
        data: yData
      }
    ]
  }

  chartInstance.value.setOption(option)
}

// 渲染饼图
const renderPieChart = () => {
  if (!chartInstance.value || !props.optionStatistics?.length) return

  const pieData = props.optionStatistics.map((item, index) => ({
    name: item.itemText || `选项${index + 1}`,
    value: item.selectCount || 0,
    percentage: item.selectRate || 0
  }))

  const option = {
    title: {
      show: false
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        return `${params.name}<br/>选择人数: ${params.value}<br/>选择率: ${params.data.percentage}%`
      }
    },
    legend: {
      orient: 'vertical',
      right: '5%',
      top: 'center',
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        fontSize: 12
      },
      formatter: function(name) {
        // 截断过长的图例文本
        return name.length > 15 ? name.substring(0, 15) + '...' : name
      }
    },
    series: [
      {
        name: '选择统计',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['35%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'outside',
          formatter: function(params) {
            return `${params.percent}%`
          },
          fontSize: 11
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        color: getChartColors(),
        data: pieData
      }
    ]
  }

  chartInstance.value.setOption(option)
}

// 渲染图表
const renderChart = () => {
  nextTick(() => {
    if (chartType.value === 'bar') {
      renderBarChart()
    } else {
      renderPieChart()
    }
  })
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  // 销毁已存在的实例
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }

  // 创建新实例
  chartInstance.value = echarts.init(chartRef.value)
  
  // 渲染图表
  renderChart()

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

// 监听数据变化
watch(
  () => [props.optionStatistics, props.totalAnswers],
  () => {
    renderChart()
  },
  { deep: true }
)

// 生命周期
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

onBeforeUnmount(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
    chartInstance.value = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.question-chart-container {
  background: #fafafa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.chart-header {
  margin-bottom: 16px;
}

.question-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.question-number {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.question-type-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.chart-switch {
  margin-left: auto;
}

.question-text {
  color: #333;
  margin-bottom: 8px;
  line-height: 1.5;
  font-size: 14px;
}

.question-meta {
  margin-bottom: 10px;
}

.answer-count {
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  padding: 2px 8px;
  border-radius: 4px;
}

.chart-wrapper {
  width: 100%;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.options-detail {
  margin-top: 16px;
  background: white;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e8e8e8;
}

.option-detail-item {
  margin-bottom: 12px;
}

.option-detail-item:last-child {
  margin-bottom: 0;
}

.option-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.option-text {
  color: #333;
  font-size: 13px;
  flex: 1;
  margin-right: 12px;
}

.option-stats {
  color: #666;
  font-size: 12px;
  white-space: nowrap;
}

.option-progress {
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .question-chart-container {
    padding: 15px;
  }
  
  .question-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .chart-switch {
    margin-left: 0;
  }
  
  .chart-wrapper {
    height: 250px !important;
  }
  
  .option-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style> 