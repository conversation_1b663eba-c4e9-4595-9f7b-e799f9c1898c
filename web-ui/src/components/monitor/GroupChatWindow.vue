<template>
  <div class="group-chat-window">
    <div class="chat-header">
      <span class="group-name">{{ groupInfo.groupName }}</span>
      <span class="student-count">{{ groupInfo.studentCount || 0 }}人</span>
    </div>
    
    <div 
      class="chat-messages" 
      ref="messagesContainer"
      @scroll="handleScroll" style="max-height: 200px;" :id="'chat-window-'+groupInfo.groupId">
      
      <!-- 加载更多指示器 -->
      <div v-if="loadMoreState !== 'hidden'" class="load-more-indicator">
        <div v-if="loadMoreState === 'loading'" class="loading-more">
          <i class="el-icon-loading"></i>
          <span>加载更多消息中...</span>
        </div>
        <div v-else-if="loadMoreState === 'finished'" class="no-more">
          <span>没有更多历史消息了</span>
        </div>
        <div v-else-if="loadMoreState === 'error'" class="load-error" @click="retryLoadMore">
          <i class="el-icon-refresh"></i>
          <span>加载失败，点击重试</span>
        </div>
      </div>
      
      <!-- 初始加载指示器 -->
      <div v-if="loading" class="loading-indicator">
        <i class="el-icon-loading"></i>
        <span>加载历史消息中...</span>
      </div>
      
      <div 
        v-for="message in messages" 
        :key="message.id"
        class="message-item"
        :class="{ 'system-message': message.type === 'SYSTEM' }">
        
        <!-- 普通聊天消息 -->
        <div v-if="message.type === 'CHAT'" class="chat-message">
          <div class="message-avatar">
            <img v-if="message.avatar" :src="message.avatar" :alt="message.sender" />
            <div v-else class="default-avatar">{{ getAvatarText(message.sender) }}</div>
          </div>
          <div class="message-content">
            <div class="message-header">
              <span class="sender-name">{{ message.displayName || message.sender }}</span>
              <span class="message-time">{{ formatTime(message.timestamp) }}</span>
            </div>
            <div class="message-text">{{ message.content }}</div>
          </div>
        </div>
        
        <!-- 系统消息 -->
        <div v-else-if="message.type === 'SYSTEM'" class="system-message-content">
          <i class="el-icon-info"></i>
          <span>{{ message.content }}</span>
          <span class="message-time">{{ formatTime(message.timestamp) }}</span>
        </div>
      </div>
      
      <!-- 无消息提示 -->
      <div v-if="messages.length === 0" class="no-messages" style="height: 300px;">
        <i class="el-icon-chat-line-square"></i>
        <p>暂无聊天消息</p>
      </div>
    </div>
    
    <div class="chat-footer">
      <el-button size="small" class="custom-button" @click="handleFullscreen" :id="'el-icon-full-screen-'+groupInfo.groupId">
        <i class="el-icon-full-screen"></i>
        全屏查看
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getGroupChatHistory } from '@/api/scenario/group'

// Props
const props = defineProps({
  groupInfo: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['fullscreen'])

// 响应式数据
const messages = ref([])
const messagesContainer = ref(null)
const maxMessages = 500 // 增加最大消息数量限制
const loading = ref(false)
const hasLoadedHistory = ref(false)

// 分页相关状态
const currentPage = ref(1)
const pageSize = ref(50)
const hasMoreMessages = ref(true)
const isLoadingMore = ref(false)
const loadMoreState = ref('hidden') // hidden, loading, finished, error

// 生成默认头像文字
const getAvatarText = (sender) => {
  if (!sender) return '?'
  return sender.length > 0 ? sender.charAt(0).toUpperCase() : '?'
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  
  // 处理字符串类型的时间戳
  let timestampNum = timestamp
  if (typeof timestamp === 'string') {
    timestampNum = parseInt(timestamp)
  }
  
  // 检查时间戳是否有效
  if (isNaN(timestampNum) || timestampNum <= 0) {
    return ''
  }
  
  // 确保是13位时间戳（毫秒）
  if (timestampNum.toString().length === 10) {
    timestampNum = timestampNum * 1000
  }
  
  const date = new Date(timestampNum)
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return ''
  }
  
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / 60000)
  
  if (diffMins < 1) {
    return '刚刚'
  } else if (diffMins < 60) {
    return `${diffMins}分钟前`
  } else if (diffMins < 1440) {
    const diffHours = Math.floor(diffMins / 60)
    return `${diffHours}小时前`
  } else {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

// 添加新消息
const addMessage = (message) => {
  // 检查是否为重复消息（避免历史消息和实时消息重复）
  const messageTimestamp = parseInt(message.timestamp) || 0
  const existingMessage = messages.value.find(
    m => {
      const existingTimestamp = parseInt(m.timestamp) || 0
      return existingTimestamp === messageTimestamp && 
             m.sender === message.sender && 
             m.content === message.content &&
             m.type === message.type
    }
  )
  
  if (existingMessage) {
    console.log('[GroupChatWindow] 跳过重复消息:', message)
    return
  }
  
  // 生成唯一ID
  const messageWithId = {
    ...message,
    id: `realtime-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    displayName: message.nickname || message.sender,
    isHistory: false
  }
  
  // 按时间戳顺序插入消息
  const insertIndex = messages.value.findIndex(
    m => parseInt(m.timestamp) > messageTimestamp
  )
  
  if (insertIndex === -1) {
    // 新消息时间戳最大，追加到末尾
    messages.value.push(messageWithId)
  } else {
    // 需要插入到指定位置保持时间顺序
    messages.value.splice(insertIndex, 0, messageWithId)
  }
  
  // 限制消息数量
  if (messages.value.length > maxMessages) {
    messages.value.splice(0, messages.value.length - maxMessages)
  }
  
  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
}

// 滚动到底部
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 重置分页状态
const resetPagination = () => {
  currentPage.value = 1
  hasMoreMessages.value = true
  isLoadingMore.value = false
  loadMoreState.value = 'hidden'
}

// 加载群组历史消息（首次加载）
const loadChatHistory = async () => {
  if (!props.groupInfo.groupId || loading.value || hasLoadedHistory.value) {
    return
  }
  
  loading.value = true
  resetPagination()
  
  try {
    console.log('[GroupChatWindow] 加载群组历史消息:', props.groupInfo.groupId)
    
    const response = await getGroupChatHistory(props.groupInfo.groupId, 1, pageSize.value)
    
    if (response.code === 200 && response.data) {
      const historyMessages = response.data.map(msg => ({
        ...msg,
        id: `history-${msg.timestamp}-${Math.random().toString(36).substr(2, 9)}`,
        displayName: msg.nickname || msg.sender,
        isHistory: true
      }))
      
      // 检查是否还有更多消息
      if (historyMessages.length < pageSize.value) {
        hasMoreMessages.value = false
      } else {
        hasMoreMessages.value = true
      }
      
      // 按时间戳排序（正序，早期消息在前）
      historyMessages.sort((a, b) => {
        const timeA = parseInt(a.timestamp) || 0
        const timeB = parseInt(b.timestamp) || 0
        return timeA - timeB
      })
      
      // 设置历史消息
      messages.value = historyMessages
      hasLoadedHistory.value = true
      currentPage.value = 1
      
      console.log(`[GroupChatWindow] 成功加载 ${historyMessages.length} 条历史消息，是否有更多: ${hasMoreMessages.value}`)
      
      // 滚动到底部
      nextTick(() => {
        scrollToBottom()
      })
    } else {
      console.warn('[GroupChatWindow] 加载历史消息失败:', response.msg)
    }
  } catch (error) {
    console.error('[GroupChatWindow] 加载历史消息异常:', error)
    ElMessage.error('加载历史消息失败')
  } finally {
    loading.value = false
  }
}

// 加载更多历史消息
const loadMoreMessages = async () => {
  if (isLoadingMore.value || !hasMoreMessages.value || !props.groupInfo.groupId) {
    return
  }
  
  console.log(`[GroupChatWindow] 加载更多消息，第 ${currentPage.value + 1} 页`)
  
  isLoadingMore.value = true
  loadMoreState.value = 'loading'
  
  // 记录当前第一条消息，用于保持滚动位置
  const firstMessage = messages.value.length > 0 ? messages.value[0] : null
  const scrollTop = messagesContainer.value?.scrollTop || 0
  
  try {
    const response = await getGroupChatHistory(
      props.groupInfo.groupId, 
      currentPage.value + 1, 
      pageSize.value
    )
    
    if (response.code === 200) {
      let newMessages = Array.isArray(response.data) ? response.data : []
      
      // 检查是否还有更多消息
      if (newMessages.length < pageSize.value) {
        hasMoreMessages.value = false
        loadMoreState.value = 'finished'
      } else {
        loadMoreState.value = 'hidden'
      }
      
      if (newMessages.length > 0) {
        // 处理新消息
        newMessages = newMessages.map(msg => ({
          ...msg,
          id: `history-${msg.timestamp}-${Math.random().toString(36).substr(2, 9)}`,
          displayName: msg.nickname || msg.sender,
          isHistory: true
        }))
        
        // 按时间戳正序排列新消息
        newMessages.sort((a, b) => {
          const timeA = parseInt(a.timestamp) || 0
          const timeB = parseInt(b.timestamp) || 0
          return timeA - timeB
        })
        
        // 将新消息插入到当前消息列表的前面
        messages.value = [...newMessages, ...messages.value]
        
        // 更新页码
        currentPage.value++
        
        // 保持滚动位置
        nextTick(() => {
          if (firstMessage && messagesContainer.value) {
            // 查找原来第一条消息的新位置
            const messageElements = messagesContainer.value.querySelectorAll('.message-item')
            for (let i = 0; i < messageElements.length; i++) {
              const element = messageElements[i]
              const messageId = element.querySelector('.message-content')?.getAttribute('data-id')
              if (messageId === firstMessage.id) {
                // 滚动到这个位置
                element.scrollIntoView({ block: 'start' })
                break
              }
            }
          }
        })
        
        console.log(`[GroupChatWindow] 成功加载 ${newMessages.length} 条更多消息，总计: ${messages.value.length}`)
      } else {
        hasMoreMessages.value = false
        loadMoreState.value = 'finished'
      }
    } else {
      console.error('[GroupChatWindow] 加载更多消息失败:', response.msg)
      loadMoreState.value = 'error'
    }
  } catch (error) {
    console.error('[GroupChatWindow] 加载更多消息异常:', error)
    loadMoreState.value = 'error'
  } finally {
    setTimeout(() => {
      isLoadingMore.value = false
    }, 200)
    
    // 如果没有更多消息了，3秒后隐藏提示
    if (!hasMoreMessages.value) {
      setTimeout(() => {
        if (loadMoreState.value === 'finished') {
          loadMoreState.value = 'hidden'
        }
      }, 3000)
    }
  }
}

// 处理滚动事件
const handleScroll = (event) => {
  const container = messagesContainer.value
  if (!container) return
  
  // 检查是否滚动到顶部（距离顶部小于50px）
  if (container.scrollTop < 50 && !isLoadingMore.value && hasMoreMessages.value && messages.value.length > 0) {
    console.log('[GroupChatWindow] 滚动到顶部，触发加载更多')
    loadMoreMessages()
  }
}

// 重试加载更多
const retryLoadMore = () => {
  if (loadMoreState.value === 'error') {
    console.log('[GroupChatWindow] 重试加载更多消息')
    loadMoreMessages()
  }
}

// 处理全屏查看
const handleFullscreen = () => {
  emit('fullscreen', props.groupInfo)
}



// 监听群组变化，重新加载历史消息
watch(
  () => props.groupInfo.groupId,
  (newGroupId, oldGroupId) => {
    if (newGroupId && newGroupId !== oldGroupId) {
      console.log('[GroupChatWindow] 群组变化，重新加载历史消息:', newGroupId)
      // 重置状态
      messages.value = []
      hasLoadedHistory.value = false
      resetPagination()
      // 加载新群组的历史消息
      loadChatHistory()
    }
  },
  { immediate: false }
)

// 组件挂载时加载历史消息
onMounted(() => {
  if (props.groupInfo.groupId) {
    console.log('[GroupChatWindow] 组件挂载，加载历史消息:', props.groupInfo.groupId)
    loadChatHistory()
  }
})

// 暴露方法给父组件
defineExpose({
  addMessage,
  clearMessages: () => { 
    messages.value = []
    hasLoadedHistory.value = false
    resetPagination()
  },
  getMessagesCount: () => messages.value.length,
  reloadHistory: loadChatHistory
})
</script>

<style scoped>
.group-chat-window {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  transition: box-shadow 0.2s;
  background: white;
  display: flex;
  flex-direction: column;
  height: 400px;
}

.group-chat-window:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.chat-header {
  background-color: #f8f9fa;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.chat-header .group-name {
  font-weight: 500;
  color: #333;
}

.chat-header .student-count {
  font-size: 12px;
  color: #666;
  background-color: #ffeaea;
  padding: 2px 8px;
  border-radius: 10px;
  border: 1px solid rgba(189, 4, 7, 0.3);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: #fafafa;
  min-height: 0;
}

.message-item {
  margin-bottom: 16px;
}

.message-item:last-child {
  margin-bottom: 0;
}

.chat-message {
  display: flex;
  gap: 12px;
}

.message-avatar {
  flex-shrink: 0;
  width: 36px;
  height: 36px;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.default-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: rgba(189, 4, 7, 0.1);
  color: rgba(189, 4, 7, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  border: 2px solid rgba(189, 4, 7, 0.2);
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 4px;
}

.sender-name {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.message-text {
  color: #555;
  line-height: 1.4;
  word-wrap: break-word;
  /* background: white; */
  /* padding: 8px 12px; */
  /* border-radius: 8px; */
  /* border: 1px solid #e0e0e0; */
}

.system-message-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  font-size: 13px;
  color: #1e40af;
}

.system-message-content i {
  color: #3b82f6;
}

.no-messages {
  text-align: center;
  color: #999;
  padding: 40px 20px;
}

.no-messages i {
  font-size: 48px;
  margin-bottom: 8px;
  display: block;
  color: #ddd;
}

.no-messages p {
  margin: 0;
  font-size: 14px;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #666;
  font-size: 14px;
}

.loading-indicator i {
  margin-right: 8px;
  animation: rotating 1s linear infinite;
}

.load-more-indicator {
  padding: 10px 16px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 13px;
}

.loading-more i {
  margin-right: 6px;
  animation: rotating 1s linear infinite;
}

.no-more {
  color: #999;
  font-size: 12px;
}

.load-error {
  color: rgba(189, 4, 7, 1);
  font-size: 13px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s;
}

.load-error:hover {
  color: rgba(189, 4, 7, 0.8);
}

.load-error i {
  margin-right: 6px;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.chat-footer {
  padding: 12px 16px;
  background-color: #f8f9fa;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e0e0e0;
  flex-shrink: 0;
}

/* 自定义按钮样式 */
.custom-button {
  color: rgba(189, 4, 7, 1) !important;
  background: none !important;
  border: none !important;
  box-shadow: none !important;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px !important;
  font-size: 12px;
}

.custom-button:hover {
  color: rgba(189, 4, 7, 0.8) !important;
  background-color: rgba(189, 4, 7, 0.05) !important;
  border: none !important;
}

.custom-button:focus {
  color: rgba(189, 4, 7, 1) !important;
  background-color: rgba(189, 4, 7, 0.05) !important;
  border: none !important;
  box-shadow: none !important;
}

.custom-button i {
  font-size: 12px;
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(189, 4, 7, 0.3);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(189, 4, 7, 0.5);
}
</style> 