import { defineStore } from 'pinia';

export const useBreadcrumbStore = defineStore('breadcrumb', {
  state: () => ({
    breadcrumbs: []
  }),
  actions: {
    setBreadcrumbs(newBreadcrumbs) {
      this.breadcrumbs = newBreadcrumbs;
    },
    addBreadcrumb(breadcrumbItem) {
      this.breadcrumbs.push(breadcrumbItem);
    },
    clearBreadcrumbs() {
      this.breadcrumbs = [];
    }
  }
});

export default useBreadcrumbStore