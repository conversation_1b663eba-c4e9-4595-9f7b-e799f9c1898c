<template>
  <!-- 头部栏组件 -->
  <ScreenLayout 
    :leftPanelWidth="'200px'"
  :leftDefaultIcon="'Menu'" 
  :leftDefaultTitle="menuTitle" 
  :menuItems="menuItems" 
   @item-select="onItemSelect"
  :selectedItemKey="selectedItemKey" 
  :rightDefaultTitle="title"
  :rightDefaultIcon="'HomeFilled'"
  :breadTitles="breadTitles"
  
  >
    <template #right-content>
      <router-view/>
    </template>
    <template  #fab>
      <div></div>
    </template>
  </ScreenLayout>

</template>

<script setup>
import ScreenLayout from "@/components/ScreenLayout/index.vue";
import {ref, onMounted,provide,reactive,computed } from "vue";
import { useRouter } from 'vue-router';
// 全局面包屑导航
import { useBreadcrumbStore } from '@/store/modules/breadcrumb';
const breadcrumbStore = useBreadcrumbStore();

const title = ref("进入课堂")
const menuTitle = ref("互动教学")
const menuItems = ref([
  { id: 1,name:"课程信息",icon:'Files',path: '/scenario/index/scene' },
  { id: 2,name:"课程阶段",icon:'DocumentCopy',path:'/scenario/index/course' },
  { id: 3,name:"学员分组",icon:'User',path: '/scenario/index/scene' },
  { id: 4,name:"问卷调查",icon:'Tickets',path: '/scenario/index/scene' },
  { id: 5,name:"课堂任务",icon:'DocumentChecked',path: '/scenario/index/scene' },
  { id: 6,name:"大屏同步",icon:'Platform',path: '/scenario/index/scene' },
])

const breadTitles = computed(() => {
    return breadcrumbStore.breadcrumbs || [];
});

const onItemSelect = (item) => {
  // 处理接收到的 item
  console.log('选中的项目是:', item)
  router.push(item.path)
  selectedItemKey.value = item.id
}
const selectedItemKey = ref(1)
const router = useRouter();


</script>

<style scoped lang="scss">
@import "@/assets/styles/scene.scss";

</style>
