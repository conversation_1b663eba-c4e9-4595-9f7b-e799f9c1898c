<template>
  <el-dialog :title="title" v-model="visible" :width="width" append-to-body>
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="80px">
      <el-form-item label="课程名称" prop="courseName">
        <el-input v-model="formData.courseName" placeholder="请输入课程名称" />
      </el-form-item>
      <el-form-item label="场景名称" prop="sceneId">
        <el-select
            v-model="formData.sceneId"
            placeholder="请选择场景"
            :loading="sceneLoading"
            :disabled="!(title === '添加课程')"
        >
          <el-option
              v-for="item in sceneList"
              :key="item.sceneId"
              :label="item.sceneName"
              :value="item.sceneId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="班级名称" prop="classCode">
        <el-select
            v-model="formData.classCode"
            placeholder="请选择班级"
            :loading="classLoading"
            :disabled="!(title === '添加课程')"
        >
          <el-option
              v-for="item in classList"
              :key="item.CLASS_CODE"
              :label="item.CLASS_NAME"
              :value="item.CLASS_CODE"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="课程介绍" prop="courseIntroduction">
        <el-input
            type="textarea"
            :rows="10"
            v-model="formData.courseIntroduction"
            placeholder="请输入课程介绍"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitting">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import {addCourse, updateCourse, listClass, getCourse} from '@/api/scenario/course'
import {listScene} from "@/api/scenario/scene";

const props = defineProps({
  // 对话框标题
  title: {
    type: String,
    default: '编辑课程'
  },
  // 对话框宽度
  width: {
    type: String,
    default: '500px'
  },
  // 是否显示对话框
  modelValue: {
    type: Boolean,
    default: false
  },
  // 课程ID（编辑时传入）
  courseId: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits([
  'update:modelValue',
  'submit-success',
  'closed'
])

// 表单引用
const formRef = ref(null)
// 表单数据
const formData = reactive({
  courseName: '',
  sceneId: '',
  classCode: '',
  courseIntroduction: ''
})
// 验证规则
const rules = reactive({
  courseName: [{ required: true, message: '请输入课程名称', trigger: 'blur' }],
  sceneId: [{ required: true, message: '请选择场景', trigger: 'change' }],
  classCode: [{ required: true, message: '请选择班级', trigger: 'change' }]
})

// 下拉选项数据
const sceneList = ref([])
const classList = ref([])
// 加载状态
const sceneLoading = ref(false)
const classLoading = ref(false)
const submitting = ref(false)

// 获取场景列表
const fetchSceneList = async () => {
  try {
    sceneLoading.value = true
    const res = await listScene()
    sceneList.value = res.data || []
  } catch (error) {
    console.error('获取场景列表失败:', error)
  } finally {
    sceneLoading.value = false
  }
}

// 获取班级列表
const fetchClassList = async () => {
  try {
    classLoading.value = true
    const res = await listClass()
    classList.value = res.data || []
  } catch (error) {
    console.error('获取班级列表失败:', error)
  } finally {
    classLoading.value = false
  }
}

// 获取课程详情（编辑时）
const fetchCourseDetail = async (id) => {
  if (!id) return
  try {
    const res = await getCourse(id)
    Object.assign(formData, res.data)
  } catch (error) {
    console.error('获取课程详情失败:', error)
  }
}

// 提交表单
const submitForm = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    const api = props.courseId ? updateCourse : addCourse
    const res = await api(formData)

    emit('submit-success', res.data)
    close()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const close = () => {
  emit('update:modelValue', false)
  emit('closed')
  resetForm()
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    courseName: '',
    sceneId: '',
    classCode: '',
    courseIntroduction: ''
  })
}

// 控制对话框显示
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 初始化数据
onMounted(() => {
  fetchSceneList()
  fetchClassList()
})

// 监听courseId变化
watch(() => props.courseId, (newVal) => {
  if (newVal) {
    fetchCourseDetail(newVal)
  } else {
    resetForm()
  }
}, { immediate: true })
</script>