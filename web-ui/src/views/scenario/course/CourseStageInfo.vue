<template>
  <div class="course-stage-container">
    <StageBaseInfo
        :stage-list="stages"
        :loading="loading"
        @add-stage="openAddDialog"
        @edit-stage="openEditDialog"
        @delete-stage="confirmDeleteStage"
    />

    <!-- 添加或修改场景阶段对话框 -->
    <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        :width="dialogWidth"
        :close-on-click-modal="false"
        append-to-body
    >
      <el-form
          ref="stageFormRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
      >
        <el-form-item label="序号" prop="courseStageOrder">
          <el-input-number
              v-model="formData.courseStageOrder"
              :min="0"
              :controls="false"
              placeholder="请输入排序号"
          />
        </el-form-item>
        <el-form-item label="阶段名称" prop="courseStageTitle">
          <el-input
              v-model="formData.courseStageTitle"
              placeholder="请输入阶段名称"
              maxlength="50"
              show-word-limit
          />
        </el-form-item>
        <el-form-item label="阶段描述" prop="courseStageText">
          <el-input
              type="textarea"
              :rows="6"
              v-model="formData.courseStageText"
              placeholder="请输入阶段描述"
              maxlength="500"
              show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import StageBaseInfo from '@/views/scenario/StageBaseInfo.vue'
import { useRoute } from 'vue-router'
import {addStage, listStage, updateStage,delStage} from "../../../api/scenario/coursestage";

const props = defineProps({
  courseId: {
    type: [String, Number],
    default: null
  },
  // 可以自定义对话框宽度
  dialogWidth: {
    type: String,
    default: '500px'
  }
})

const emit = defineEmits(['add-stage', 'edit-stage', 'delete-stage', 'update'])

const route = useRoute()
const stages = ref([])

const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const stageFormRef = ref(null)

// 表单数据
const formData = reactive({
  courseStageid: undefined,
  courseStageOrder: 1,
  courseStageTitle: '',
  courseStageText: ''
})

// 表单验证规则
const formRules = reactive({
  courseStageOrder: [
    { required: true, message: '请输入排序号', trigger: 'blur' }
  ],
  courseStageTitle: [
    { required: true, message: '请输入阶段名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在2到50个字符', trigger: 'blur' }
  ],
  courseStageText: [
    { required: true, message: '请输入阶段描述', trigger: 'blur' },
    { min: 5, max: 500, message: '长度在5到500个字符', trigger: 'blur' }
  ]
})

// 获取阶段列表
const fetchStageList = async () => {
  try {
    loading.value = true
    // API调用
    const courseId = props.courseId || route.params.courseId
    const response = await listStage({ courseId: courseId })
    if(response.data){
      response.data.forEach(item =>{
        let courseStage = ref({})
        courseStage.stageId = item.courseStageId
        courseStage.stageOrder = item.courseStageOrder
        courseStage.stageTitle = item.courseStageTitle
        courseStage.stageText = item.courseStageText
        stages.value.push(courseStage)
      })
    }else{
      stages.value = []
    }
  } catch (error) {
    console.error('获取阶段列表失败:', error)
    ElMessage.error('获取阶段列表失败')
  } finally {
    loading.value = false
  }
}

// 打开添加对话框
const openAddDialog = () => {
  resetForm()
  dialogTitle.value = '添加阶段'
  dialogVisible.value = true
}

// 打开编辑对话框
const openEditDialog = (stage) => {
  debugger
  Object.assign(formData, JSON.parse(JSON.stringify(stage)))
  dialogTitle.value = '编辑阶段'
  dialogVisible.value = true
}

// 确认删除阶段
const confirmDeleteStage = async (id) => {
  debugger
  try {
    await ElMessageBox.confirm('确定要删除该阶段吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // 删除API调用
    await delStage(id)
    ElMessage.success('删除成功')

    // 删除后的列表更新
    await fetchStageList()
    emit('delete-stage', id)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const submitForm = async () => {
  try {
    await stageFormRef.value.validate()

    if (dialogTitle.value === '添加阶段') {
      // 添加API调用
      debugger
      formData.courseId = props.courseId || route.params.courseId
      await addStage({ ...formData })
      ElMessage.success('添加成功')

      // 添加后的列表更新
      await fetchStageList()
      // emit('add-stage', newStage)
    } else {
      // 编辑API调用
      await updateStage(formData)
      ElMessage.success('修改成功')

      // 编辑后的列表更新
      await fetchStageList()
      emit('edit-stage', { ...formData })
    }

    dialogVisible.value = false
    emit('update')
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    courseStageId: undefined,
    courseStageOrder: stages.value.length > 0
        ? Math.max(...stages.value.map(item => item.courseStageOrder)) + 1
        : 1,
    courseStageTitle: '',
    courseStageText: ''
  })
}
// 组件挂载时获取数据
onMounted(() => {
  const id = props.courseId || route.params.courseId
  if (id) {
    fetchStageList()
  }
})

// 暴露方法给父组件
defineExpose({
  refreshList: fetchStageList,
  getStages: () => stages.value
})
</script>

<style scoped>
.course-stage-container {
  padding: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>