<template>
  <div>
    <BaseInfo
        :obj="courseData"
        @edit-obj="handleEdit"
        @delete-obj="handleDelete"
    />
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import BaseInfo from "@/views/scenario/BaseInfo.vue"
import { getCourse } from "@/api/scenario/course.js"

const props = defineProps({
  // 课程ID，如果不传则从路由自动获取
  courseId: {
    type: [String, Number],
    default: null
  },
  // 图片基础URL
  baseUrl: {
    type: String,
    default: ''
  },
  // 是否可编辑
  editable: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'edit',
  'delete',
  'open',
  'data-loaded' // 当数据加载完成时触发
])

const route = useRoute()
const courseData = ref({
  objid: '',
  objName: '',
  objIspublic: 'N',
  objImage: '',
  objIntroduction: '',
  objCanEdit: false
})

// 获取课程信息
const fetchCourseInfo = async (id) => {
  try {
    const response = await getCourse(id)
    const data = response.data
    courseData.value = {
      objId: data.courseId,
      objName: data.courseName,
      objIspublic: data.courseIspublic,
      objImage: data.courseImage,
      objIntroduction: data.courseIntroduction,
      objCanEdit: props.editable
    }
    emit('data-loaded', courseData.value)
  } catch (error) {
    console.error('获取课程信息失败:', error)
  }
}

// 事件处理
const handleEdit = () => emit('edit', courseData.value)
const handleDelete = () => emit('delete', courseData.value)
// 初始化加载数据
onMounted(() => {
  const id = props.courseId || route.params.courseId
  if (id) {
    fetchCourseInfo(id)
  }
})

// 监听路由变化（如果使用路由参数）
watch(() => route.params.courseId, (newId) => {
  if (newId && !props.courseId) {
    fetchCourseInfo(newId)
  }
})

// 监听外部传入的courseId变化
watch(() => props.courseId, (newId) => {
  if (newId) {
    fetchCourseInfo(newId)
  }
})

// 必须显式暴露方法，否则父组件无法访问
defineExpose({
  fetchCourseInfo
})
</script>