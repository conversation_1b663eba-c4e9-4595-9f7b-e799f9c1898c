<template>
  <div>
    <div v-if="tagList.length === 0">
      <el-empty description="暂无数据"></el-empty>
    </div>
    <div class="tag-box" v-else v-for="(tag, index) in tagList" :key="tag.TAG_ID" >
      <div style="display: flex;">
        <div style="margin-bottom: 17px;
                  margin-top: 17px;
                  font-weight: 700;
                  line-height: 16px;" >{{ tag.TAG_NAME }}（{{tag.coursesList.length}}）</div>
        <div class="top-right-btn" style="display: flex; align-items: center;margin-right: 12px">
          <a  v-if="!tag.openall && openTag.indexOf(index) < 0 " @click="handleOpenAll(index)">展开全部</a>
          <a  v-else @click="handleOpenAll(index)">收起</a>
        </div>
      </div>
      <div :class="tag.openall || openTag.indexOf(index) >= 0 ?'tag-scene-box':'tag-scene-box close'">
        <div class="scene-box" v-for="(course) in tag.coursesList" :key="course.courseId">
          <div class="img-title-box">
            <div class="img-wrapper">
              <el-image class="img-box" :src="baseUrl + course.sceneImage">
                <template #error>
                  <img src="/src/assets/images/no-png.jpg" alt="">
                </template>
              </el-image>
              <!-- 蒙层部分 -->
              <div class="overlay" @click.stop>
                <div class="overlay-content">
                  <!-- 课程设置按钮 -->
                  <el-button class="course-setting-btn" @click="handleCourseSetting(course)" >课程设置</el-button>
                  <!-- 进入课堂按钮 -->
                  <el-button class="enter-class-btn" @click="handleEditSceneForTag(course, tag)" >进入课堂</el-button>
                </div>
              </div>
            </div>
            <a @click="handleCourseSetting(course)" style="color: #666;font-size: 16px;">{{course.courseName}}</a>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 添加课程对话框 -->
  <CourseEditDialog
      v-model="courseOpen"
      :title="courseTitle"
      @submit-success="submitCourseForm"
      @closed="cancel"
  />
</template>

<script setup>
import CourseEditDialog from "@/views/scenario/course/CourseEditDialog.vue"
import { listCourse} from "@/api/scenario/course.js";
import {ref,reactive, toRefs,onMounted,inject} from "vue";
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API );

const { proxy } = getCurrentInstance()
const router = useRouter();
const loading = ref(true)

const courseOpen = ref(false)
const courseTitle = ref("")

const tagList = ref([]);
const openTag = ref([])//展开的分组


const data = reactive({
  courseForm: {},
  courseRules: {
    courseName: [{ required: true, message: "课程名称不能为空", trigger: "blur" }],
    sceneId: [{ required: true, message: "场景不能为空", trigger: "blur" }],
    classCode: [{ required: true, message: "班级不能为空", trigger: "blur" }],
    courseIntroduction: [{ required: true, message: "课程介绍不能为空", trigger: "blur" }]
  },
})
const { courseForm,courseRules } = toRefs(data)



/** 查询场景课程列表 */
function getTagWithCourseList() {
  loading.value = true
  listCourse().then(response => {
    tagList.value = response.data
    loading.value = false
  })
}

// 表单重置
function reset() {
  courseForm.value = {}
  proxy.resetForm("courseRef")
}

/** 新增按钮操作 */
const addOption=()=> {
  courseOpen.value = true
  courseTitle.value = "添加课程"
}

// 取消按钮
function cancel() {
  courseOpen.value = false
  reset()
}

/** 课程提交按钮 */
function submitCourseForm() {
  getTagWithCourseList()
}

const handleOpenAll = (index) => {
  tagList.value[index].openall = !tagList.value[index].openall
  if(tagList.value[index].openall){
    openTag.value.push(index)
  }else{
    openTag.value.splice(openTag.value.indexOf(index), 1)
  }
}
function handleCourseSetting(course) {
  const courseId = course.courseId
  router.push("course-info/" + courseId );
}
onMounted(() => {
  getTagWithCourseList()
})

// 注入父组件提供的函数
const registerChildMethod = inject('registerChildMethod');
// 注册子组件方法
registerChildMethod(addOption);

</script>

<style scoped lang="scss">
@import "@/assets/styles/scene.scss";

</style>

