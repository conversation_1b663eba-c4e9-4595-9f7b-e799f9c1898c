<template>
  <div class="stage-base-info">
    <!-- 有数据时显示时间线 -->
    <div v-if="stageList.length > 0">
      <ul class="el-timeline" style="margin-left: -32px;">
        <li class="el-timeline-item" v-for="(stage, index) in stageList" :key="stage.id || index">
          <!-- 虚线连接线 -->
          <div class="timeline-connector"></div>

          <!-- 圆形序号按钮 -->
          <el-button class="stage-index-button">
            {{ index + 1 }}
          </el-button>

          <!-- 阶段内容 -->
          <div class="stage-content">
            <div class="stage-header">
              <div class="stage-title">{{ stage.stageTitle }}</div>
              <div class="stage-actions">
                <el-button
                    link
                    type="primary"
                    icon="Edit"
                    @click.stop="handleEditStage(stage)"
                >
                  修改
                </el-button>
                <el-button
                    link
                    type="primary"
                    icon="Delete"
                    @click.stop="handleDelStage(stage.stageId)"
                >
                  删除
                </el-button>
              </div>
            </div>
            <div class="stage-text">
              {{ stage.stageText }}
            </div>
          </div>
        </li>
      </ul>

      <!-- 添加阶段按钮 -->
      <div class="add-button-container">
        <el-button type="primary" @click="handleAddStage">添加阶段</el-button>
      </div>
    </div>

    <!-- 无数据时显示空状态 -->
    <div v-else class="empty-container">
      <div class="empty-add-button">
        <el-button type="primary" @click="handleAddStage">添加阶段</el-button>
      </div>
      <el-empty description="暂无数据"></el-empty>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, defineEmits } from 'vue'

interface StageItem {
  stageId?: string | number
  stageTitle: string
  stageText: string
}

const props = defineProps({
  stageList: {
    type: Array as () => StageItem[],
    default: () => []
  }
})

const emit = defineEmits([
  'addStage',
  'editStage',
  'deleteStage'
])

const handleAddStage = () => {
  emit('addStage')
}

const handleEditStage = (stage: StageItem) => {
  emit('editStage', stage)
}

const handleDelStage = (id: string | number) => {
  emit('deleteStage', id)
}
</script>

<style scoped>
.stage-base-info {
  width: 100%;
}

.timeline-connector {
  border-left: 2px dashed var(--el-timeline-node-color);
  height: 100%;
  left: 15px;
  position: absolute;
}

.stage-index-button {
  width: 30px;
  height: 30px;
  align-items: center;
  border-radius: 50%;
  position: absolute;
  padding: 0;
  display: flex;
  justify-content: center;
}

.stage-content {
  margin-right: 20px;
  padding-left: 30px;
  position: relative;
  width: 100%;
}

.stage-header {
  margin-bottom: 8px;
  font-size: 16px;
  margin-left: 6px;
  display: flex;
  padding-top: 3px;
  font-weight: 700;
}

.stage-title {
  flex: 1;
}

.stage-actions {
  display: flex;
  margin-left: auto;
}

.stage-text {
  margin-left: 6px;
  color: var(--el-text-color-regular);
}

.add-button-container {
  margin-top: 20px;
  padding-bottom: 30px;
}

.empty-container {
  text-align: center;
}

.empty-add-button {
  margin-bottom: 20px;
}
</style>