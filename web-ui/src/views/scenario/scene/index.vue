<template>

      <div>
        <div v-if="tags.length == 0" >
          <el-empty description="暂无数据"></el-empty>
        </div>
        <div class="tag-box" v-else v-for="(tag, index) in tags" :key="tag.tagId" >
          <div style="display: flex;">
            <div style="margin-bottom: 17px;
                      margin-top: 17px;
                      font-weight: 700;
                      line-height: 16px;" >{{ tag.tagName }}</div>
            <div class="top-right-btn" style="display: flex; align-items: center;margin-right: 12px">
              <a v-if="!tag.openall && openedTags.indexOf(index) < 0 " @click="handleOpenAll(index)">展开全部</a>
              <a v-else @click="handleOpenAll(index)">收起</a>
            </div>
          </div>
          <div class="tag-scene-box" v-if="sceneMap.get(tag.tagId) && sceneMap.get(tag.tagId).length === 0">
            <div class="scene-box" >
              <a @click="handleAddSceneForTag(tag)" class="add-scene">添加场景</a>
            </div>
          </div>
          <div :class="tag.openall || openedTags.indexOf(index) >= 0 ?'tag-scene-box':'tag-scene-box close'" v-else>
            <div class="scene-box" v-for="(scene) in sceneMap.get(tag.tagId)" :key="scene.sceneId">
              <div class="img-title-box">
              <div class="public" v-if="scene.sceneIspublic == 'Y'">公共</div>
              <div class="img-wrapper" @click="handleOpenScene(scene)">
                <el-image class="img-box" :src="baseUrl + scene.sceneImage">
                  <template #error>
                    <img src="/src/assets/images/no-png.jpg" alt="">
                  </template>
                </el-image>
                <!-- 蒙层部分 -->
                  <div class="overlay" @click.stop>
                    <div class="overlay-content">
                      <!-- 课程设置按钮 -->
                      <el-button class="course-setting-btn" @click="handleEditSceneForTag(scene,tag)" >修改场景</el-button>
                      <!-- 进入课堂按钮 -->
                      <el-button class="enter-class-btn" @click="handleDelSceneForTag(scene,tag)" >删除场景</el-button>
                    </div>
                  </div>
              </div>
                
              <a style="color: #666;font-size: 16px;"  @click="handleOpenScene(scene)">{{scene.sceneName}}</a>
             </div>
            </div>
            <div class="scene-box" >
              <a @click="handleAddSceneForTag(tag)" class="add-scene">添加场景</a>
            </div>
          </div>
        </div>
        <!-- 管理场景分类对话框 -->
        <el-dialog :title="tagTitle" v-model="tagOpen" width="500px" append-to-body>
          <el-form ref="tagRef" :model="tagsForm" label-width="80px">
            <div>
              <div style="margin: 0 0 10px 0;text-align: left;">
                <el-button icon="Plus" @click="handleAddTagClick">新增</el-button>
                <span style="margin-left: 5px;">提示：点击分类名修改，拖拽分类排序</span>
              </div>
              <Draggable v-model="tempTags" style="display: grid;grid-template-columns: 1fr 1fr;" >
                <template #item="{element,index}">
                  <div style="width: 200px;">
                    <el-input v-if="editableTag[index]" :ref="el => (inputEditRef[index] = el)" v-model="element.tagName" style="width: 200px;margin: 0 10px 5px 0;" placeholder="请输入分类名称" @mousemove="handleInputEditChange(element,index)"/>
                    <el-tag v-else closable :disable-transitions="false" size="large" style="width: 200px;margin: 0 10px 5px 0;" @click="showEditTagInput(index)" @close="handleDelTag(element, index)" >
                      {{ element.tagName }}
                    </el-tag>
                  </div>
                </template>
              </Draggable >
            </div>
          </el-form>
          <template #footer>
            <div class="dialog-footer">
              <el-button type="primary" @click="submitTagForm">确 定</el-button>
              <el-button @click="cancel">关 闭</el-button>
            </div>
          </template>
        </el-dialog>
        <!-- 添加或修改场景对话框 -->
        <SceneEditDialog
            v-model="sceneOpen"
            :title="sceneTitle"
            :tag-id="currentTagId"
            :scene-id="currentSceneId"
            @submit-success="submitSceneFormSuccess"
            @closed="handleClosed"
        />
      </div>
   

</template>

<script setup >
import { listScene, getScene, delScene, addScene, updateScene,tagsList,editTag,delTag } from "@/api/scenario/scene"
import {ref,toRefs,inject,reactive} from "vue"
import SceneEditDialog from '@/views/scenario/scene/SceneEditDialog.vue'
import Draggable from "vuedraggable";
import {useRouter} from "vue-router";

const tagTitle = ref("")
const tagOpen = ref(false)
const tempTags = ref([])


const router = useRouter();
const { proxy } = getCurrentInstance()

const baseUrl = ref(import.meta.env.VITE_APP_BASE_API );

const sceneMap = ref(new Map())
const sceneOpen = ref(false)
const sceneTitle = ref("")
const currentTagId = ref(null)
const currentSceneId = ref(null)

const tags = ref([])
const openedTags = ref([])

const inputEditRef = ref([])
const editableTag = ref([])

const data = reactive({
  tagsForm: {},
  sceneForm: {}
})

const { tagsForm, sceneForm } = toRefs(data)

/**弹出框取消**/
function cancel() {
  tagOpen.value = false
  sceneOpen.value = false
  reset()
}

/**重置**/
function reset() {
  tagsForm.value = {
  }
  sceneForm.value = {
  }
  proxy.resetForm("tagRef")
}

/**添加场景**/
const handleAddSceneForTag = (tag) => {
  sceneOpen.value = true
  currentTagId.value = tag.tagId
  sceneTitle.value = "添加场景"
}
/**修改场景**/
const handleEditSceneForTag = (scene,tag) => {
  sceneOpen.value = true
  currentSceneId.value = scene.sceneId
  currentTagId.value = tag.tagId
  sceneTitle.value = "修改场景"
}
/**删除分类**/
function handleDelSceneForTag(scene,tag) {
  const sceneId = scene.sceneId
  proxy.$modal.confirm('是否确认删除所选数据项？').then(function() {
    return delScene(sceneId)
  }).then(() => {
    getTags()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}
/**打开场景详情**/
const handleOpenScene = (scene,tag) => {
  const sceneId = scene.sceneId
  router.push("scene-info/" + sceneId );
}
/**展开全部**/
const handleOpenAll = (index) => {
  tags.value[index].openall = !tags.value[index].openall
  if(tags.value[index].openall){
    openedTags.value.push(index)
  }else{
    openedTags.value.splice(openedTags.value.indexOf(index), 1);
  }
}
/**添加标签*/
const handleAddTagClick = () => {
  const lastIndex = tempTags.value.length+1
  tempTags.value.push({tagId: "",tagName: "新增分类"+lastIndex})
}
/**将分类innput去掉**/
const handleInputEditChange = async (tag, index) => {
  editableTag.value[index] = false
}
//编辑标签信息 input显示
const showEditTagInput = index => {
  editableTag.value[index] = true
  nextTick(() => {
    inputEditRef.value[index].input.focus()
  })
}
/**删除标签**/
function handleDelTag(tag,index) {
  proxy.$modal.confirm('是否确认删除？').then(function() {
    if(tag.tagId){
      return delTag(tag.tagId)
    }
  }).then(() => {
   tempTags.value.splice(index, 1);
   proxy.$modal.msgSuccess("删除成功")
   getTags()
  }).catch(() => {})
}


/** 新增按钮操作 */
function handleManageTag() {
  tagOpen.value = true
  tagTitle.value = "管理场景分类"
}
// 注入父组件提供的函数
const registerChildMethod = inject('registerChildMethod');
// 注册子组件方法
registerChildMethod(handleManageTag);

/** 分类提交按钮 */
function submitTagForm() {
  let canSubmit = true
  if(tempTags.value.length == 0){
    proxy.$modal.msgError("分类不能为空")
    canSubmit = false
    return false
  }
  tempTags.value.forEach((item,index) => {
    tempTags.value[index].tagIndex = index
    if(item.tagName === ''){
      proxy.$modal.msgError("分类不能为空")
      canSubmit = false
      return false
    }
  })
  if(canSubmit){
    editTag(tempTags.value).then(response => {
      proxy.$modal.msgSuccess("操作成功")
      cancel()
      getTags()
    })
  }
}

/** 提交按钮 */
async function submitSceneFormSuccess() {
  getTags()
}

function handleClosed() {
  currentSceneId.value = null
  currentTagId.value = null
}

/**获取标签**/
function getTags(){
  tagsList().then(response => {
    tags.value = response.data
    tempTags.value = response.data
    tags.value.forEach(item =>{
      listScene({sceneTagid: item.tagId}).then(response =>{
        sceneMap.value.set(item.tagId,response.data)
      })
    })
  });
}
getTags()
</script>

<style scoped lang="scss">
@import "@/assets/styles/scene.scss";

/* 局部样式 */
.tag-scene-box > .scene-box  .public {
  width: 40px;
  border-radius: 0px 6px 0px 6px;
  background: rgb(255, 173, 72);
  color: #FFFFFF;
  z-index: 999;
  position: absolute;
  right: 0px;
  height: 22px;
}


/*.image-slot >.el-icon {
  width: 300px;
  height: 200px;
  font-size: 100px;
  color: lightgray;
}*/

</style>
