<!-- SceneEditDialog.vue -->
<template>
  <el-dialog :title="title" :model-value="modelValue" width="500px" append-to-body @update:model-value="$emit('update:modelValue', $event)">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="80px">
      <el-form-item label="场景名称" prop="sceneName">
        <el-input v-model="formData.sceneName" placeholder="请输入场景名称" />
      </el-form-item>
      <el-form-item label="公共场景" prop="sceneIspublic">
        <el-radio-group v-model="formData.sceneIspublic">
          <el-radio v-for="dict in sys_yes_no" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="场景图片" prop="sceneImage">
        <image-upload :limit="1" v-model="formData.sceneImage"/>
      </el-form-item>
      <el-form-item label="场景介绍" prop="sceneIntroduction">
        <el-input rows="10" v-model="formData.sceneIntroduction" placeholder="请输入场景介绍" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import {addScene, updateScene, getScene} from "@/api/scenario/scene";
const { proxy } = getCurrentInstance()
const { sys_yes_no } = proxy.useDict("sys_yes_no");

const props = defineProps({
  // 对话框标题
  title: {
    type: String,
    default: '编辑场景'
  },
  // 对话框宽度
  width: {
    type: String,
    default: '500px'
  },
  // 是否显示对话框
  modelValue: {
    type: Boolean,
    default: false
  },//分类Id
  tagId: {
    type: [String, Number],
    default: null
  },
  // 场景ID（编辑时传入）
  sceneId: {
    type: [String, Number],
    default: null
  }
})


const emit = defineEmits([
  'update:modelValue',
  'submit-success',
  'closed'
])

// 表单引用
const formRef = ref(null)
// 表单数据
const formData = reactive({
  sceneName: '',
  sceneIspublic: '',
  sceneImage: '',
  sceneIntroduction: ''
})
// 验证规则
const rules = reactive({
  sceneName: [{ required: true, message: '请输入场景名称', trigger: 'blur' }],
  sceneIspublic: [{ required: true, message: '请选择是否公共场景场景', trigger: 'change' }],
  sceneImage: [{ required: true, message: '请选择场景图片', trigger: 'change' }],
  sceneIntroduction: [{ required: true, message: '请输入场景介绍', trigger: 'change' }]
})

const submitting = ref(false)


// 获取场景详情（编辑时）
const fetchSceneDetail = async (id) => {
  if (!id) return
  try {
    const res = await getScene(id)
    Object.assign(formData, res.data)
  } catch (error) {
    console.error('获取课程详情失败:', error)
  }
}

// 提交表单
const submitForm = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    const api = props.sceneId ? updateScene : addScene
    if(formData.sceneTagid == null){
      formData.sceneTagid = props.tagId
    }
    const res = await api(formData)

    emit('submit-success', res.data)
    close()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const close = () => {
  emit('update:modelValue', false)
  emit('closed')
  resetForm()
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    sceneName: '',
    sceneIspublic: '',
    sceneImage: '',
    sceneIntroduction: ''
  })
}

// 控制对话框显示
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 初始化数据
onMounted(() => {
})

// 监听sceneId变化
watch(() => props.sceneId, (newVal) => {
  if (newVal) {
    fetchSceneDetail(newVal)
  } else {
    resetForm()
  }
}, { immediate: true })
</script>