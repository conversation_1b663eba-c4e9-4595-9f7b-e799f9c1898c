<template>
  <div>
    <BaseInfo
        :obj="sceneData"
        @edit-obj="handleEdit"
        @delete-obj="handleDelete"
    />
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import BaseInfo from "@/views/scenario/BaseInfo.vue"
import { getScene } from "@/api/scenario/scene.js"

const props = defineProps({
  // 课程ID，如果不传则从路由自动获取
  sceneId: {
    type: [String, Number],
    default: null
  },
  // 图片基础URL
  baseUrl: {
    type: String,
    default: ''
  },
  // 是否可编辑
  editable: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'edit',
  'delete',
  'open',
  'data-loaded' // 当数据加载完成时触发
])

const route = useRoute()
const sceneData = ref({
  objid: '',
  objName: '',
  objIspublic: 'N',
  objImage: '',
  objIntroduction: '',
  objCanEdit: false
})

// 获取课程信息
const fetchSceneInfo = async (id) => {
  try {
    const response = await getScene(id)
    const data = response.data
    sceneData.value = {
      objId: data.sceneId,
      objName: data.sceneName,
      objIspublic: data.sceneIspublic,
      objImage: data.sceneImage,
      objIntroduction: data.sceneIntroduction,
      objCanEdit: props.editable
    }
    emit('data-loaded', sceneData.value)
  } catch (error) {
    console.error('获取课程信息失败:', error)
  }
}

// 事件处理
const handleEdit = () => emit('edit', sceneData.value)
const handleDelete = () => emit('delete', sceneData.value)
// 初始化加载数据
onMounted(() => {
  const id = props.sceneId || route.params.sceneId
  if (id) {
    fetchSceneInfo(id)
  }
})

// 监听路由变化（如果使用路由参数）
watch(() => route.params.sceneId, (newId) => {
  if (newId && !props.sceneId) {
    fetchSceneInfo(newId)
  }
})

// 监听外部传入的sceneId变化
watch(() => props.sceneId, (newId) => {
  if (newId) {
    fetchSceneInfo(newId)
  }
})

// 必须显式暴露方法，否则父组件无法访问
defineExpose({
  fetchSceneInfo
})
</script>