<template>
  <!-- 头部栏组件 -->
  <ScreenLayout 
:leftPanelWidth="'200px'"
  :leftDefaultIcon="'Menu'" 
  :leftDefaultTitle="menuTitle" 
  :menuItems="menuItems" 
   @item-select="onItemSelect"
  :selectedItemKey="selectedItemKey" 
  :rightDefaultTitle="title"
  :rightDefaultIcon="'HomeFilled'"
  :breadTitles="breadTitles"
  
  >
    <template #right-header-extra>
      <div style="margin-left: auto;height: 17px;margin-bottom: 5px;">
        <el-button icon="Tools" v-if="selectedItemKey==1"  type="primary"  @click="handleManageTag">分类管理</el-button>
        <el-button icon="Tools" v-if="selectedItemKey==2"  type="primary"  @click="handleManageTag">添加课程</el-button>
      </div>
    </template>
    <template #right-content>
      <router-view ref="childComponent"/>
    </template>
    <template  #fab>
      <div></div>
    </template>
  </ScreenLayout>

</template>

<script setup>
import ScreenLayout from "@/components/ScreenLayout/index.vue";
import {ref, onMounted,provide,reactive,computed } from "vue";
import { useRouter } from 'vue-router';
// 全局面包屑导航
import { useBreadcrumbStore } from '@/store/modules/breadcrumb';
const breadcrumbStore = useBreadcrumbStore();

const title = ref("我的场景")
const menuTitle = ref("互动教学")
const menuItems = ref([
  { id: 1,name:"场景管理",icon:'Box',path: '/scenario/index/scene' },
  { id: 2,name:"课程管理",icon:'CreditCard',path:'/scenario/index/course' }
])
const home=reactive( { id: 1, name: "互动教学" })
const breadTitles = computed(() => {
    return breadcrumbStore.breadcrumbs || [];
});

const onItemSelect = (item) => {
  // 处理接收到的 item
  console.log('选中的项目是:', item)
  router.push(item.path)
  selectedItemKey.value = item.id
  // breadTitles.length = 0; // 清空面包屑
  // breadTitles.push(home);
  // breadTitles.push(item);
}
const selectedItemKey = ref(1)

const router = useRouter();
// 定义 childComponent ref
const childComponent = ref(null);

/** 新增按钮操作 */
const handleManageTag=()=> {
  callChildMethod();
}
let callChildMethod = () => {};
// 调用子组件方法
provide('registerChildMethod', (method)=>{
  callChildMethod = method;
});

</script>

<style scoped lang="scss">
@import "@/assets/styles/scene.scss";

/* 局部样式 */
.tag-scene-box > .scene-box  .public {
  width: 40px;
  border-radius: 0px 6px 0px 6px;
  background: rgb(255, 173, 72);
  color: #FFFFFF;
  z-index: 999;
  position: absolute;
  right: 0px;
  height: 22px;
}


.image-slot >.el-icon {
  width: 300px;
  height: 200px;
  font-size: 100px;
  color: lightgray;
}

</style>
