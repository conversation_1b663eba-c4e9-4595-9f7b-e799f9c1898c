<template>
  <div>
    <div style="display: flex; align-items: center; justify-content: space-between;">
      <div style="display: flex; align-items: center; gap: 8px;">
        <span style="font-weight: 700;">{{ obj.objName }}</span>
        <span v-if="obj.objIspublic === 'Y'" style="color: var(--el-text-color-secondary); font-size: 0.9em;">
          (公共场景)
        </span>
      </div>
      <div v-if="obj.objCanEdit">
        <el-button link type="primary" icon="Edit" @click="handleEditObj">修改</el-button>
        <el-button link type="primary" icon="Delete" @click="handleDelObj">删除</el-button>
      </div>
    </div>

    <div class="img_box" style="margin: 12px 0; cursor: pointer;">
      <el-image class="bigImg" :src="baseUrl + obj.objImage" style="width: 100%; height: auto;">
        <template #error>
          <img src="/src/assets/images/no-png.jpg" alt="图片" style="width: 100%; height: 100%; object-fit: cover;">
        </template>
      </el-image>
    </div>

    <div style="color: var(--el-text-color-regular); font-size: 0.95em;">
      {{ obj.objIntroduction }}
    </div>
  </div>
</template>

<script setup>
import {defineProps, defineEmits, ref} from 'vue';
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API );
const props = defineProps({
  obj: {
    type: Object,
    required: true,
    default: () => ({
      objName: '',
      objIspublic: 'N',
      objImage: '',
      objIntroduction: ''
    })
  },
  baseUrl: {
    type: String,
    default: ''
  }
});

const emit = defineEmits([
  'edit-obj',
  'delete-obj',
  'open-obj'
]);

const handleEditObj = () => {
  emit('edit-obj', props.obj);
};

const handleDelObj = () => {
  emit('delete-obj', props.obj);
};

</script>

<style scoped>
.img_box {
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid var(--el-border-color-light);
  transition: all 0.3s;
}

.img_box:hover {
  box-shadow: var(--el-box-shadow-light);
  border-color: var(--el-border-color-extra-light);
}

.bigImg {
  display: block;
  width: 100%;
  height: 180px;
  object-fit: cover;
}
</style>