<template>
  <div class="chat-monitor-page">
    <!-- 头部栏组件 -->
    <ScreenHeader />

    <!-- 退出全屏按钮 -->
    <el-button 
      id="exit-fullscreen-btn"
      v-if="fullscreenGroupInfo" 
      class="exit-fullscreen-btn"
      type="primary" 
      plain
      @click="handleFullscreenClose">
      退出全屏
    </el-button>

    <!-- 主要内容区域 -->
    <div class="main-content" :class="{ 'fullscreen-mode': !!fullscreenGroupInfo }">
      <!-- 左侧群组列表 -->
      <div class="left-panel">
        <div class="panel-header">
          <el-icon><Menu color="rgba(189, 4, 7, 1)" /></el-icon>
          <h3>群组列表</h3>
        </div>
        <div class="group-list">
          <div 
            v-for="group in groupList" 
            :key="group.groupId"
            class="group-item"
            :class="{ active: selectedGroupId === group.groupId }"
            @click="selectGroup(group)" :id="'group-list-'+group.groupId">
            <div class="group-info">
              <div class="group-name">
                <el-icon><User /></el-icon>
                <span>{{ group.groupName }}</span>
              </div>
            </div>
            <div class="group-checkbox">
              <el-checkbox 
                :id="'group-list-ck-'+group.groupId"
                :model-value="selectedGroups.includes(group.groupId)"
                @update:model-value="(checked) => handleGroupToggle(group.groupId, checked)"
                
                class="custom-checkbox">
              </el-checkbox>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧群组聊天窗口区域 -->
      <div class="right-panel">
        <div class="panel-header">
          <el-icon><HomeFilled /></el-icon>
          <h3>
            聊天窗口
          </h3>
          <WebSocketManager 
            :selected-groups="selectedGroups"
            @message-received="handleMessageReceived"
            @connection-status-change="handleConnectionStatusChange"
            ref="wsManager" />
        </div>
        <div class="chat-windows-grid" id="chat-windows-grid">
          <div class="chat-window-wrapper"
               v-for="group in displayedGroups"
               :key="group.groupId"
               :class="{'fullscreen-target': fullscreenGroupInfo && fullscreenGroupInfo.groupId === group.groupId}">
            <GroupChatWindow
              :group-info="group"
              :is-fullscreen="fullscreenGroupInfo && fullscreenGroupInfo.groupId === group.groupId"
              :ref="el => setChatWindowRef(el, group.groupId)"
              @fullscreen="handleFullscreen" />
          </div>
        </div>
        
        <!-- 无选中群组提示 -->
        <div v-if="displayedGroups.length === 0 && !fullscreenGroupInfo" class="no-selected-groups">
          <i class="el-icon-chat-line-square"></i>
          <p>请在左侧勾选要监控的群组</p>
        </div>
      </div>
    </div>

    <!-- 悬浮按钮, 全屏时隐藏 -->
    <ScreenFab v-show="!fullscreenGroupInfo" @fab-click="handleFabClick" />
  </div>
</template>

<script setup name="ChatMonitor">
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'
import { getMyDeptGroups, getGroupsByClassCode, getGroupsByCourseCode } from '@/api/scenario/group'
import ScreenHeader from '@/components/ScreenHeader/index.vue'
import WebSocketManager from '@/components/monitor/WebSocketManager.vue'
import GroupChatWindow from '@/components/monitor/GroupChatWindow.vue'
import ScreenFab from '@/components/ScreenFab/index.vue'

// 路由实例
const route = useRoute()

// 响应式数据
const loading = ref(false)
const selectedGroupId = ref('')
const selectedGroups = ref([])

// 群组列表
const groupList = ref([])

// localStorage 键名
const STORAGE_KEY = 'monitor_selected_groups'

// 全屏模式状态
const fullscreenGroupInfo = ref(null)

// 计算属性：显示的群组（只显示选中的）
const displayedGroups = computed(() => {
  return groupList.value.filter(group => selectedGroups.value.includes(group.groupId))
})

// 选择群组
const selectGroup = (group) => {
  selectedGroupId.value = group.groupId
  // ElMessage.info(`选中群组: ${group.groupName}`)
}

// 处理群组勾选切换
const handleGroupToggle = (groupId, checked) => {
  if (checked) {
    // 添加到选中列表
    if (!selectedGroups.value.includes(groupId)) {
      selectedGroups.value.push(groupId)
    }
  } else {
    // 从选中列表移除
    const index = selectedGroups.value.indexOf(groupId)
    if (index > -1) {
      selectedGroups.value.splice(index, 1)
    }
  }
  
  // 保存到 localStorage
  localStorage.setItem(STORAGE_KEY, JSON.stringify(selectedGroups.value))
}

// 从 localStorage 恢复选中状态
const restoreSelectedGroups = () => {
  try {
    const saved = localStorage.getItem(STORAGE_KEY)
    if (saved) {
      selectedGroups.value = JSON.parse(saved)
    }
  } catch (error) {
    console.error('恢复选中状态失败:', error)
    selectedGroups.value = []
  }
}

// 获取群组数据
const fetchGroups = async () => {
  loading.value = true
  try {
    const code = route.query.code
    let response
    
    if (code) {
      // 如果有代码，使用课程代码查询
      response = await getGroupsByCourseCode(code)
    } else {
      // 否则使用原有的方法查询当前教师所在班级的群组
      response = await getMyDeptGroups()
    }
    
    if (response.code === 200) {
      groupList.value = response.data || []
      // 恢复选中状态
      restoreSelectedGroups()
      ElMessage.success('群组数据加载成功')
    } else {
      ElMessage.error(response.msg || '获取群组数据失败')
    }
  } catch (error) {
    console.error('获取群组数据出错:', error)
    ElMessage.error('获取群组数据失败')
  } finally {
    loading.value = false
  }
}

// WebSocket管理器和聊天窗口引用
const wsManager = ref(null)
const chatWindowRefs = ref(new Map())

// 设置聊天窗口引用
const setChatWindowRef = (el, groupId) => {
  if (el) {
    chatWindowRefs.value.set(groupId, el)
  } else {
    chatWindowRefs.value.delete(groupId)
  }
}

// 处理WebSocket消息接收
const handleMessageReceived = (message) => {
  console.log('[Monitor] 收到聊天消息:', message)
  
  // 将消息转发给对应的聊天窗口
  const chatWindow = chatWindowRefs.value.get(message.groupId)
  if (chatWindow) {
    chatWindow.addMessage(message)
  }
}

// 处理WebSocket连接状态变化
const handleConnectionStatusChange = (status) => {
  console.log('[Monitor] WebSocket连接状态变化:', status)
}

// 处理全屏查看
const handleFullscreen = (groupInfo) => {
  console.log('[Monitor] 打开全屏聊天窗口:', groupInfo)
  fullscreenGroupInfo.value = groupInfo
}

// 处理全屏窗口关闭
const handleFullscreenClose = () => {
  fullscreenGroupInfo.value = null
}

// 处理悬浮按钮点击
const handleFabClick = (item) => {
  console.log('悬浮按钮点击:', item)
  // ScreenFab组件已经处理了路由跳转，这里可以做额外的处理
}

// 生命周期
onMounted(() => {
  fetchGroups()
})
</script>

<style scoped>
.chat-monitor-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  
}

/* 退出全屏按钮 */
.exit-fullscreen-btn {
  position: fixed;
  top: 16px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2000;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  margin: 0 auto;
  gap: 0px;
  flex-direction: row;
  width: 100vw;
  height: calc(100vh - 64px);
  background: url('@/assets/images/screen/entry-background.png') no-repeat 100% 100%;
}

/* 全屏模式下的布局调整 */
.main-content.fullscreen-mode {
  padding: 0;
  gap: 0;
}

.fullscreen-mode .left-panel {
  display: none;
}

.fullscreen-mode .right-panel {
  width: 100vw;
  height: calc(100vh - 64px); /* 占据除头部外的所有可视区域 */
  border-radius: 0;
  box-shadow: none;
}

.fullscreen-mode .right-panel .panel-header {
  display: none;
}

.fullscreen-mode .chat-windows-grid {
  display: block; /* 从 grid 变为 block */
  height: 100%; /* panel-header隐藏后，占据整个right-panel的高度 */
  padding: 0;
  overflow: hidden;
}

.fullscreen-mode .chat-window-wrapper {
  display: none; /* 默认隐藏所有窗口 */
  height: 100%;
}

.fullscreen-mode .group-chat-window {
  width: 100vw;
  height: 100%;
}

.fullscreen-mode :deep(.chat-footer) {
  display: none !important;
}

.fullscreen-mode .chat-window-wrapper.fullscreen-target {
  display: block; /* 只显示全屏目标窗口 */
}

.fullscreen-mode .no-selected-groups {
  display: none;
}

/* 左侧面板 */
.left-panel {
  width: 280px;
  background: white;
  border-radius: 0px;
  box-shadow: 0;
  overflow: hidden;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.group-list {
  height: calc(100% - 62px);
  overflow-y: auto;
}

.group-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.group-item:hover {
  background-color: #F8E5E6;
}

.group-item:hover .group-name {
  color: rgba(189, 4, 7, 1);
}

.group-item.active {
  /* background-color: #F8E5E6; */
  /* border-left: 3px solid rgba(189, 4, 7, 1); */
}

.group-icon {
  width: 40px;
  height: 40px;
  background-color: rgba(189, 4, 7, 1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 12px;
}

.group-info {
  flex: 1;
}

.group-name {
  font-weight: normal;
  color: #333;
  margin-bottom: 4px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 6px;
}

.group-detail {
  font-size: 12px;
  color: #666;
}

.group-checkbox {
  margin-left: 12px;
}

/* 自定义复选框样式 */
.custom-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: rgba(189, 4, 7, 1);
  border-color: rgba(189, 4, 7, 1);
}

.custom-checkbox :deep(.el-checkbox__inner:hover) {
  border-color: rgba(189, 4, 7, 1);
}

/* 右侧面板 */
.right-panel {
  flex: 1;
  /* background: #F3F3F3; */
  border-radius: 0;
  box-shadow: 0;
  overflow: hidden;
  display: flex; /* 使用 flex 布局 */
  flex-direction: column; /* 垂直排列 */
}

.panel-header {
  padding: 15px 20px;
  display: flex;
  justify-content: flex-start;
  gap: 8px;
  align-items: center;
  font-weight:bolder;
  font-size: 22px;
}

.chat-windows-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px;
  flex: 1; /* 占据剩余空间 */
  overflow-y: auto;
  background-color: #Ffffff;
  margin: 0 20px 20px 20px;
}

.chat-window-wrapper {
  /*
    在 Grid 布局下，内容项的高度默认会被拉伸以填充整个单元格。
    GroupChatWindow 组件内部设置了固定的高度或最大高度 (e.g., height: 400px)，
    这导致它不会自动填充父级 .chat-window-wrapper 的空间。
    当切换到全屏模式时，.chat-window-wrapper 会占据整个可用高度，
    但内部的 GroupChatWindow 仍然保持其原始尺寸。

    为了解决这个问题，我们需要确保 .chat-window-wrapper 在全屏模式下，
    能够强制其子元素 (GroupChatWindow) 扩展到100%的高度。
    通过将 .chat-window-wrapper 设置为 flex 容器，
    我们可以让 GroupChatWindow (作为其唯一的 flex 项) 自动拉伸填充所有可用空间。
  */
  display: contents; /* 默认情况下，wrapper不影响布局 */
}

.fullscreen-mode .chat-window-wrapper.fullscreen-target {
  display: flex; /* 在全屏模式下，使用flex来拉伸子元素 */
}

.no-selected-groups {
  text-align: center;
  color: #999;
  padding: 60px 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.no-selected-groups i {
  font-size: 64px;
  margin-bottom: 16px;
  color: #ddd;
}

.no-selected-groups p {
  margin: 0;
  font-size: 16px;
}

/* 自定义按钮样式 */
.custom-button {
  color: rgba(189, 4, 7, 1) !important;
}

.custom-button:hover {
  color: rgba(189, 4, 7, 0.8) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    padding: 10px;
  }
  
  .left-panel {
    width: 100%;
    margin-bottom: 20px;
  }
  
  .screenshots-grid {
    grid-template-columns: 1fr;
  }
}
</style> 