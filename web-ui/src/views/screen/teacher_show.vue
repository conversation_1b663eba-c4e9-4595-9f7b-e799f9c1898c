<template>
    <div style="width: 100%; height: 100%;" ref="routerViewContainer">

          <!-- <div class="screen-a">
            <p v-if="serverState.connected">Connected to server!</p>
            <p v-if="serverState.error">{{ serverState.error }}</p>
            <p>Device ID: {{ deviceId }}；Screen:{{ windowWidth }},{{ windowHeight }}</p>
            <p>{{ state.lastMousePosition.x }},{{ state.lastMousePosition.y }}----{{ state.lastMousePosition.percentX}},{{ state.lastMousePosition.percentY }}</p>
        </div>

        
        <div class="point" :style="'left:'+ state.lastMousePosition.x +'px; top:'+ state.lastMousePosition.y +'px;'">

        </div> -->
        
       <router-view/>
    </div>
</template>
<script setup>
import { ref, reactive, onMounted,onUpdated,onUnmounted  } from 'vue';
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
// 路由实例
const route = useRoute()
// 设备ID
const deviceId = ref(crypto.randomUUID());
console.log('Device ID:', deviceId.value);


// 最终的 状态记录
const state = reactive({
  clickCount: 0,
  lastMousePosition: {
    x: 0,
    y: 0,
    percentX:0,
    percentY:0
  },
  scrollTop: 0,
 
});

const serverState=ref({
    connected: false,
    error: null
});

const routerViewContainer = ref(null);
let observer = null;
onMounted(() => {
    connectServer();
    window.addEventListener('resize', handleResize);
    window.addEventListener('click', handleClick);
    addCheckboxListeners();
    // debouncedAddDivScrollListeners();
    if (routerViewContainer.value) {
    observer = new MutationObserver(() => {
      debouncedAddDivScrollListeners();
       window.addEventListener('click', handleClick);
       addCheckboxListeners();
    });

    observer.observe(routerViewContainer.value, {
      childList: true,
      subtree: true
    });
  }
});

onUnmounted(() => {
  if (observer) {
    observer.disconnect();
  }
});

// 窗口大小处理
const windowWidth = ref(window.innerWidth);
const windowHeight = ref(window.innerHeight);

// 处理窗口大小变化的函数
const handleResize = () => {
  windowWidth.value = window.innerWidth;
  windowHeight.value = window.innerHeight;
};



let ws=null;
const code= route.query.code;

const baseUrl = import.meta.env.VITE_APP_BIG_SCREEN_URL;

const connectServer = () => {
    
    ws= new WebSocket(baseUrl + code );
    ws.onopen = () => {
          console.log('WebSocket connection established:'+code);
          serverState.value.connected = true;
    };
   
    ws.onerror = (error) => {
         ElMessage.error('WebSocket error:'+ error)
        console.error('WebSocket error:', error);
        serverState.value.error = 'WebSocket error: ' + error.message;
    };
};

let mouseMoveTimer = null;
// 鼠标移动
const handleMouseMove = (event) => {
  // 节流处理，每100ms发送一次
  if (!mouseMoveTimer) {
    mouseMoveTimer = setTimeout(() => {
     

      const screenX = event.clientX; // 相对于屏幕左侧的水平坐标
      const screenY = event.clientY; // 相对于屏幕顶部的垂直坐标
      // 计算百分比（四舍五入保留两位小数）
      const percentX = ((screenX / windowWidth.value) * 100).toFixed(2);
      const percentY = ((screenY / windowHeight.value) * 100).toFixed(2);

       state.lastMousePosition = {
        x: Math.round(event.clientX),
        y: Math.round(event.clientY),
        percentX: percentX,
        percentY: percentY
      };


      let msg= {
        type: 'mousemove',
        deviceId: deviceId.value,
        data: {
          clientX: percentX,
          clientY: percentY
        }
      };
      sendSocketMessage(msg);
      mouseMoveTimer = null;
    }, 100);
  }
};
// 鼠标点击
const handleClick = (event) => {
       state.clickCount++;
      const screenX = event.clientX; // 相对于屏幕左侧的水平坐标
      const screenY = event.clientY; // 相对于屏幕顶部的垂直坐标

      // 计算百分比（四舍五入保留两位小数）
      const percentX = ((screenX / windowWidth) * 100).toFixed(4);
      const percentY = ((screenY / windowHeight) * 100).toFixed(4);
      // event.target.id;
    let tid=event.target.id;
    if(event.target.localName=='span'&&tid=='' )
    {
        let parentElement=event.target.parentNode;
        if (parentElement.localName && parentElement.localName.toLowerCase() === 'button') 
        {
          tid=parentElement.id;
          console.log('找到buttonid:'+tid)
        }
    }


    let msg= {
        type: 'click',
        deviceId: deviceId.value,
        data: {
            clientX: percentX,
            clientY: percentY,
            targetId: tid,
        }
    };
    console.log('Click event:', msg);
    sendSocketMessage(msg);
};
// 监听窗口滚动事件
window.addEventListener('scroll', () => {
    state.scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
    let msg= {
        type: 'scroll',
        deviceId: deviceId.value,
        data: {
            scrollTop: state.scrollTop
        }
    };
    sendSocketMessage(msg);
});

const debounce = (fn, delay) => {
  let timer = null;
  return (...args) => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
};

// 处理 div 滚动事件
const handleDivScroll = (event) => {
    const div = event.target;
    const divId = div.id || 'no_id'; // 如果 div 没有 ID，使用 'no_id'
    const scrollTop = div.scrollTop;
    const scrollLeft = div.scrollLeft;

    let msg = {
        type: 'divScroll',
        deviceId: deviceId.value,
        data: {
            divId: divId,
            scrollTop: scrollTop,
            scrollLeft: scrollLeft
        }
    };
    // console.log('发送的div scroll:'+JSON.stringify(msg))
    sendSocketMessage(msg);
};
 // 获取所有可滚动的 div 元素
const getScrollableDivs = () => {
    return Array.from(document.querySelectorAll('div')).filter(div => 
      div.scrollHeight > div.clientHeight || div.scrollWidth > div.clientWidth
    );
  };
    // 添加滚动监听器
const addDivScrollListeners = () => {
    // 先移除之前添加的监听器，避免重复添加
    // const previousScrollableDivs = document.querySelectorAll('div[data-scroll-listener]');
    // previousScrollableDivs.forEach(div => {
    //     div.removeEventListener('scroll', handleDivScroll);
    //     div.removeAttribute('data-scroll-listener');
    // });

    const scrollableDivs = getScrollableDivs();
    console.log('添加事件的div：'+scrollableDivs.length)
    scrollableDivs.forEach(div => {
        if( !div.getAttribute('data-scroll-listener'))
        {
          div.addEventListener('scroll', handleDivScroll);
          div.setAttribute('data-scroll-listener', 'true');
        }
       
    });
};

const debouncedAddDivScrollListeners = debounce(addDivScrollListeners, 500);
onUpdated(() => {
  console.log('页面已更新');
  debouncedAddDivScrollListeners();
  // 这里可添加页面更新后要执行的逻辑
});



// 添加复选框监听器
const addCheckboxListeners = () => {
  const checkboxes = document.querySelectorAll('input[type="checkbox"]');
  checkboxes.forEach(checkbox => {
    if(!checkbox.getAttribute("data-ck-listener"))
  {
    checkbox.setAttribute("data-ck-listener",true)
    checkbox.addEventListener('change', handleCheckboxChange);
  }
    
  });
};

// 处理复选框状态变化
const handleCheckboxChange = (event) => {
  const checkbox = event.target;
  const msg = {
    type: 'checkboxChange',
    deviceId: deviceId.value,
    data: {
      id: checkbox.id || 'no_id',
      checked: checkbox.checked
    }
  };
  console.log('Checkbox change event:', msg);
  sendSocketMessage(msg);
};

const sendSocketMessage = (message) => {
    if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(message));
    } else {
        ElMessage.error('WebSocket is not open. Unable to send message')
        console.error('WebSocket is not open. Unable to send message:', message);
    }
};

</script>

<style scoped>
.point{
    width: 10px;
    height: 10px;
    background-color: red;
    position: absolute;
    border-radius: 50%;
}
</style>