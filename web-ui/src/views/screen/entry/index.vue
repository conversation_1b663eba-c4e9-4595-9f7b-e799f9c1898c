<template>
  <div class="course-code-entry-page">
    <!-- 头部栏组件 -->
    <ScreenHeader />

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="entry-container">
        <div class="entry-card">
          <div class="card-header">
            <div class="header-icon">
              <el-icon><Connection /></el-icon>
            </div>
            <h2 class="card-title">课程码</h2>
            <p class="card-subtitle">请输入有效的课程码</p>
          </div>
          
          <div class="card-body">
            <el-form
              ref="codeFormRef"
              :model="codeForm"
              :rules="codeRules"
              label-position="top"
              class="code-form">
              <el-form-item label="" prop="courseCode" class="form-item">
                <el-input
                  v-model="codeForm.courseCode"
                  placeholder="请输入课程码"
                  size="large"
                  clearable
                  class="code-input"
                  :disabled="loading"
                  @keyup.enter="handleSubmit">
                  <template #prefix>
                    <i class="el-icon-document"></i>
                  </template>
                </el-input>
              </el-form-item>
              
              <el-form-item class="submit-item">
                <el-button
                  type="primary"
                  size="large"
                  class="submit-button"
                  :loading="loading"
                  @click="handleSubmit">
                  <span v-if="!loading">进入监控大屏</span>
                  <span v-else>验证中...</span>
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <div class="card-footer">
            <p class="help-text">
              <i class="el-icon-info"></i>
              课程码由系统管理员提供
            </p>
          </div>
        </div>
        
        <!-- 装饰性元素 -->
        <div v-if="false" class="decoration-elements">
          <div class="decoration-circle circle-1"></div>
          <div class="decoration-circle circle-2"></div>
          <div class="decoration-circle circle-3"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="CourseCodeEntry">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { validateCourseCode } from '@/api/teacher/monitor'
import ScreenHeader from '@/components/ScreenHeader/index.vue'

// 路由实例
const router = useRouter()

// 响应式数据
const loading = ref(false)
const codeFormRef = ref(null)

// 表单数据
const codeForm = reactive({
  courseCode: ''
})

// 表单验证规则
const codeRules = reactive({
  courseCode: [
    { required: true, message: '请输入课程码', trigger: 'blur' },
    { min: 3, max: 50, message: '课程码长度应在 3 到 50 个字符之间', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9_-]+$/, message: '课程码只能包含字母、数字、下划线和短横线', trigger: 'blur' }
  ]
})

// 提交处理
const handleSubmit = async () => {
  try {
    // 表单验证
    const valid = await codeFormRef.value.validate()
    if (!valid) return

    loading.value = true

    // 调用接口验证课程码
    const response = await validateCourseCode(codeForm.courseCode)
    
    if (response.code === 200 && response.data) {
      ElMessage.success('课程码验证成功，正在跳转...')
      
      // 延迟跳转以显示成功消息
      setTimeout(() => {
        // router.push({
        //   path: '/screen/monitor',
        //   query: { code: codeForm.courseCode }
        // })

          router.push({
          path: '/screen/max',
          query: { code: codeForm.courseCode }
        })
      }, 800)
    } else {
      ElMessage.error(response.msg || '课程码验证失败')
    }
  } catch (error) {
    console.error('课程码验证异常:', error)
    ElMessage.error('验证服务异常，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.course-code-entry-page {
  min-height: 100vh;
  background: url('@/assets/images/screen/entry-background.png') no-repeat 100% 100%;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  position: relative;
}

.entry-container {
  position: relative;
  z-index: 10;
}

.entry-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 480px;
  max-width: 90vw;
}

.card-header {
  background: linear-gradient(135deg, rgba(189, 4, 7, 1) 0%, rgba(189, 4, 7, 0.8) 100%);
  color: white;
  padding: 40px 30px 30px;
  text-align: center;
  position: relative;
}

.header-icon {
  width: 64px;
  height: 64px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  backdrop-filter: blur(10px);
}

.header-icon i {
  font-size: 28px;
}

.card-title {
  margin: 0 0 10px;
  font-size: 24px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.card-subtitle {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.5;
}

.card-body {
  padding: 40px 30px;
}

.code-form {
  width: 100%;
}

.form-item {
  margin-bottom: 30px;
}

.form-item :deep(.el-form-item__label) {
  font-weight: 600;
  color: #333;
  font-size: 16px;
  margin-bottom: 8px;
}

.code-input {
  width: 100%;
}

.code-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
  height: 48px;
}

.code-input :deep(.el-input__wrapper:hover) {
  border-color: rgba(189, 4, 7, 0.3);
}

.code-input :deep(.el-input__wrapper.is-focus) {
  border-color: rgba(189, 4, 7, 1);
  box-shadow: 0 0 0 3px rgba(189, 4, 7, 0.1);
}

.code-input :deep(.el-input__inner) {
  font-size: 16px;
  letter-spacing: 1px;
}

.submit-item {
  margin-bottom: 0;
}

.submit-button {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(189, 4, 7, 1) 0%, rgba(189, 4, 7, 0.8) 100%);
  border: none;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(189, 4, 7, 0.3);
}

.submit-button:active {
  transform: translateY(0);
}

.card-footer {
  background-color: #f8f9fa;
  padding: 20px 30px;
  text-align: center;
}

.help-text {
  margin: 0;
  font-size: 13px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.help-text i {
  color: #999;
}

/* 装饰性元素 */
.decoration-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 120px;
  height: 120px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 80px;
  height: 80px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 20px 10px;
  }
  
  .entry-card {
    width: 100%;
    margin: 0 10px;
  }
  
  .card-header {
    padding: 30px 20px 20px;
  }
  
  .card-body {
    padding: 30px 20px;
  }
  
  .card-footer {
    padding: 15px 20px;
  }
  
  .card-title {
    font-size: 20px;
  }
  
  .decoration-circle {
    display: none;
  }
}
</style> 