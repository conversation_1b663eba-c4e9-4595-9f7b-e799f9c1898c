<template>
  <ScreenLayout
    :menu-items="surveyList"
    :selected-item-key="selectedSurveyId"
    left-default-icon="Document"
    left-default-title="问卷列表"
    right-default-icon="PieChart"
    right-default-title="问卷统计"
    item-key-field="paperId"
    item-name-field="paperTitle"
    @item-select="selectSurvey">
    

    <!-- 自定义菜单项 -->
    <template #menu-item="{ item, isSelected }">
      <div class="survey-item-content">
        <div class="survey-info">
          <div class="survey-title">{{ item.paperTitle }}</div>
          <div class="survey-meta">
            <span class="scene-name">{{ item.sceneName }}</span>
            <span class="question-count">{{ item.questionCount }}题</span>
          </div>
          <div class="survey-stats">
            <span class="submission-rate">
              提交率: {{ item.submissionRate || 0 }}%
            </span>
            <span class="submission-count">
              ({{ item.submittedCount || 0 }}/{{ item.totalStudents || 0 }})
            </span>
          </div>
        </div>
        <div class="survey-status">
          <el-tag 
            :type="getStatusTagType(item.submissionRate)"
            size="small">
            {{ getStatusText(item.submissionRate) }}
          </el-tag>
        </div>
      </div>
    </template>

    <!-- 右侧头部额外内容 -->
    <template #right-header-extra>
      <div class="header-actions" v-if="selectedSurvey">
        <el-button 
          type="primary" 
          size="small" 
          @click="refreshStatistics"
          :loading="statisticsLoading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </template>
    
    <!-- 右侧内容区域 -->
    <template #right-content>
      <!-- 问卷详情内容 -->
      <div v-if="selectedSurvey" class="survey-detail">
        <!-- 基本信息 -->
        <div class="survey-basic-info">
          <h4>{{ selectedSurvey.paperTitle }}</h4>
          <div class="basic-stats">
            <div class="stat-item">
              <span class="label">总学生数</span>
              <span class="value">{{ selectedSurvey.totalStudents || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="label">已提交数</span>
              <span class="value">{{ selectedSurvey.submittedCount || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="label">提交率</span>
              <span class="value">{{ selectedSurvey.submissionRate || 0 }}%</span>
            </div>
          </div>
        </div>

        <!-- 数据加载状态 -->
        <div v-if="statisticsLoading" class="loading-container">
          <el-icon class="is-loading">
            <Loading />
          </el-icon>
          <span class="loading-text">正在加载统计数据...</span>
        </div>

        <!-- 题目统计图表 -->
        <div v-else-if="surveyStatistics?.questionStatistics?.length" class="questions-statistics">
          <div class="statistics-header">
            <h5>题目统计分析</h5>
            <div class="view-options">
              <el-radio-group v-model="viewMode" size="small">
                <el-radio-button value="chart">图表视图</el-radio-button>
                <el-radio-button value="table">表格视图</el-radio-button>
              </el-radio-group>
            </div>
          </div>

          <!-- 图表视图 -->
          <div v-if="viewMode === 'chart'" class="chart-view">
            <QuestionChart
              v-for="(question, index) in surveyStatistics.questionStatistics"
              :key="question.questionId"
              :question-id="question.questionId"
              :question-text="question.questionText"
              :question-type="question.questionType"
              :question-order="index + 1"
              :option-statistics="question.optionStatistics || []"
              :total-answers="question.totalAnswers || 0"
              height="320px"
            />
          </div>

          <!-- 表格视图 -->
          <div v-else class="table-view">
            <div 
              v-for="(question, index) in surveyStatistics.questionStatistics" 
              :key="question.questionId"
              class="question-table-item">
              <div class="question-header">
                <span class="question-number">第{{ index + 1 }}题</span>
                <span class="question-type-tag">
                  {{ question.questionType === 1 ? '单选' : '多选' }}
                </span>
              </div>
              <div class="question-text">{{ question.questionText }}</div>
              <div class="question-answers">
                <span class="answer-count">回答人数: {{ question.totalAnswers || 0 }}</span>
              </div>
              
              <!-- 选项统计表格 -->
              <el-table 
                :data="question.optionStatistics || []" 
                stripe 
                size="small"
                class="options-table">
                <el-table-column prop="itemText" label="选项内容" min-width="200" />
                <el-table-column prop="selectCount" label="选择人数" width="100" align="center" />
                <el-table-column prop="selectRate" label="选择率" width="100" align="center">
                  <template #default="scope">
                    <span>{{ scope.row.selectRate || 0 }}%</span>
                  </template>
                </el-table-column>
                <el-table-column label="占比" width="200">
                  <template #default="scope">
                    <el-progress 
                      :percentage="scope.row.selectRate || 0" 
                      :stroke-width="16"
                      :color="getProgressColor(scope.row.selectRate)"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
        
        <!-- 无统计数据提示 -->
        <div v-else class="stats-placeholder">
          <el-icon class="placeholder-icon">
            <PieChart />
          </el-icon>
          <p>暂无统计数据</p>
          <p class="placeholder-desc">该问卷还没有学生提交答案</p>
        </div>
      </div>
      
      <!-- 无选中问卷提示 -->
      <div v-else class="no-selected-survey">
        <el-icon class="placeholder-icon">
          <DocumentCopy />
        </el-icon>
        <p>请在左侧选择要查看的问卷</p>
      </div>
    </template>

    <!-- 悬浮按钮 -->
    <template #fab>
      <ScreenFab @fab-click="handleFabClick" />
    </template>
  </ScreenLayout>
</template>

<script setup name="SurveyResult">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import { Loading, Document, PieChart, DocumentCopy, Refresh } from '@element-plus/icons-vue'
import { getSurveyListByCourseCode, getSurveyStatistics } from '@/api/teacher/survey'
import ScreenLayout from '@/components/ScreenLayout/index.vue'
import ScreenFab from '@/components/ScreenFab/index.vue'
import QuestionChart from '@/components/QuestionChart/index.vue'

// 路由实例
const route = useRoute()

// 响应式数据
const loading = ref(false)
const statisticsLoading = ref(false)
const courseCode = ref('')
const selectedSurveyId = ref('')
const selectedSurvey = ref(null)
const viewMode = ref('chart') // chart | table

// 问卷列表
const surveyList = ref([])

// 问卷统计数据
const surveyStatistics = ref(null)

// 获取状态标签类型
const getStatusTagType = (submissionRate) => {
  if (submissionRate >= 80) return 'success'
  if (submissionRate >= 50) return 'warning'
  return 'danger'
}

// 获取状态文本
const getStatusText = (submissionRate) => {
  if (submissionRate >= 80) return '良好'
  if (submissionRate >= 50) return '一般'
  return '较低'
}

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage >= 60) return '#67c23a'
  if (percentage >= 30) return '#e6a23c'
  return '#f56c6c'
}

// 选择问卷
const selectSurvey = async (survey) => {
  selectedSurveyId.value = survey.paperId
  selectedSurvey.value = survey
  
  // 加载问卷统计数据
  await loadSurveyStatistics(survey.paperId)
}

// 获取问卷列表
const fetchSurveyList = async () => {
  if (!courseCode.value) {
    ElMessage.error('缺少课程代码参数')
    return
  }

  loading.value = true
  try {
    const response = await getSurveyListByCourseCode(courseCode.value)
    
    if (response.code === 200) {
      surveyList.value = response.data || []
      
      // 自动选择第一个问卷
      if (surveyList.value.length > 0) {
        await selectSurvey(surveyList.value[0])
      }
    } else {
      ElMessage.error(response.msg || '获取问卷列表失败')
    }
  } catch (error) {
    console.error('获取问卷列表出错:', error)
    ElMessage.error('获取问卷列表失败')
  } finally {
    loading.value = false
  }
}

// 加载问卷统计数据
const loadSurveyStatistics = async (paperId) => {
  if (!paperId) return

  statisticsLoading.value = true
  try {
    const response = await getSurveyStatistics(paperId)
    
    if (response.code === 200) {
      surveyStatistics.value = response.data
      console.log('问卷统计数据:', surveyStatistics.value)
    } else {
      ElMessage.error(response.msg || '获取问卷统计失败')
      surveyStatistics.value = null
    }
  } catch (error) {
    console.error('获取问卷统计出错:', error)
    ElMessage.error('获取问卷统计失败')
    surveyStatistics.value = null
  } finally {
    statisticsLoading.value = false
  }
}

// 刷新统计数据
const refreshStatistics = async () => {
  if (selectedSurveyId.value) {
    await loadSurveyStatistics(selectedSurveyId.value)
    ElMessage.success('统计数据已刷新')
  }
}

// 处理悬浮按钮点击
const handleFabClick = (item) => {
  console.log('悬浮按钮点击:', item)
}

// 生命周期
onMounted(() => {
  // 从路由参数获取课程代码
  courseCode.value = route.query.code || ''
  if (courseCode.value) {
    fetchSurveyList()
  } else {
    ElMessage.error('缺少课程代码参数')
  }
})
</script>

<style scoped>
/* 课程信息样式 */
.course-info {
  font-size: 12px;
  color: #666;
  margin-left: auto;
}

.course-code {
  background: #e3f2fd;
  padding: 4px 8px;
  border-radius: 4px;
  color: #1976d2;
}

/* 自定义菜单项样式 */
.survey-item-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.survey-info {
  flex: 1;
}

.survey-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
  font-size: 14px;
}

.survey-meta {
  display: flex;
  gap: 12px;
  margin-bottom: 4px;
}

.scene-name, .question-count {
  font-size: 12px;
  color: #666;
}

.survey-stats {
  display: flex;
  gap: 8px;
  align-items: center;
}

.submission-rate {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

.submission-count {
  font-size: 12px;
  color: #999;
}

.survey-status {
  margin-left: 12px;
}

/* 右侧内容区域样式 */
.survey-detail {
  height: 100%;
}

.survey-basic-info {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.survey-basic-info h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 18px;
}

.basic-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
  background: white;
  padding: 16px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-item .label {
  font-size: 12px;
  color: #666;
  display: block;
  margin-bottom: 8px;
}

.stat-item .value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  display: block;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
}

.loading-text {
  margin-left: 12px;
  font-size: 14px;
}

.questions-statistics {
  margin-top: 20px;
}

.statistics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f0f0;
}

.statistics-header h5 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.question-table-item {
  background: #fafafa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.question-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.question-number {
  font-weight: 600;
  color: #333;
}

.question-type-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.question-text {
  color: #333;
  margin-bottom: 10px;
  line-height: 1.5;
}

.question-answers {
  margin-bottom: 16px;
}

.answer-count {
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  padding: 4px 8px;
  border-radius: 4px;
}

.options-table {
  margin-top: 12px;
}

.stats-placeholder, .no-selected-survey {
  text-align: center;
  color: #999;
  padding: 60px 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.placeholder-icon {
  font-size: 64px;
  margin-bottom: 16px;
  color: #ddd;
}

.stats-placeholder p, .no-selected-survey p {
  margin: 8px 0;
  font-size: 16px;
}

.placeholder-desc {
  font-size: 14px !important;
  color: #bbb !important;
}

/* 头部操作按钮 */
.header-actions {
  display: flex;
  gap: 8px;
}
</style> 