<template>
  <div class="course-stages-page">
    <!-- 头部栏组件 -->
    <ScreenHeader />

    <!-- 主要内容区域 -->
    <div class="main-content">

      <!-- 课程阶段步骤展示 -->
      <div class="stages-container">

        <!-- 自定义阶段时间线 -->
        <div class="custom-stages-timeline" v-if="stageList.length > 0">
          <!-- 阶段步骤条 -->
          <div class="stages-steps">
            <div 
              v-for="(stage, index) in stageList" 
              :key="stage.courseStageId"
              class="stage-step-wrapper"
              :class="{ 'active': stage.isCompleted === 1 }">
              
              <!-- 阶段圆圈和连接线 -->
              <div class="stage-step">
                <div class="stage-circle" :class="{ 'active': stage.isCompleted === 1 }">
                  {{ index + 1 }}
                </div>
                <div v-if="index < stageList.length - 1" class="stage-line" :class="{ 'active': stage.isCompleted === 1 && stageList[index + 1] && stageList[index + 1].isCompleted === 1 }"></div>
              </div>
              
              <!-- 阶段标题 -->
              <div class="stage-title" :class="{ 'active': stage.isCompleted === 1 }">{{ stage.courseStageTitle }}</div>
            </div>
          </div>

          <!-- 所有阶段内容卡片 -->
          <div class="stages-content">
            <div 
              v-for="(stage, index) in stageList" 
              :key="stage.courseStageId"
              class="stage-content-card"
              :class="{ 
                'active': stage.isCompleted === 1,
                'selected': index === selectedStageIndex 
              }">
              
              <!-- 阶段标题 -->
              <div class="stage-card-header" :class="stage.isCompleted === 1 ? 'header-active' : 'header-pending'">
                <div class="stage-title-text">{{ stage.courseStageTitle }}</div>
              </div>
              
              <!-- 阶段内容描述 -->
              <div class="stage-card-content">
                <p v-if="stage.courseStageText">{{ stage.courseStageText }}</p>
                <p v-else class="no-content">暂无阶段内容描述</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 无阶段提示 -->
        <div v-if="stageList.length === 0 && !loading" class="no-stages">
          <i class="el-icon-warning-outline"></i>
          <h3>暂无课程阶段</h3>
          <p>该课程还未设置阶段信息</p>
        </div>
      </div>
    </div>

    <!-- 悬浮按钮 -->
    <ScreenFab @fab-click="handleFabClick" />
  </div>
</template>

<script setup name="CourseStages">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'
import { getCourseStageListByCourseCode } from '@/api/teacher/stage'
import { getCourseInfo } from '@/api/teacher/monitor'
import ScreenHeader from '@/components/ScreenHeader/index.vue'
import ScreenFab from '@/components/ScreenFab/index.vue'

// 路由实例
const route = useRoute()

// 响应式数据
const loading = ref(false)
const stageList = ref([])
const selectedStageIndex = ref(0)
const courseInfo = ref(null)

// 当前选中的阶段
const selectedStage = computed(() => {
  return stageList.value[selectedStageIndex.value] || null
})

// 当前激活的步骤（用于步骤条显示）
const currentActiveStage = computed(() => {
  // 找到最后一个已完成阶段的索引，如果都未完成则为-1
  let lastCompletedIndex = -1
  for (let i = 0; i < stageList.value.length; i++) {
    if (stageList.value[i].isCompleted === 1) {
      lastCompletedIndex = i
    }
  }
  return lastCompletedIndex
})

// 获取课程阶段数据
const fetchCourseStages = async () => {
  const courseCode = route.query.code
  if (!courseCode) {
    ElMessage.error('缺少课程代码参数')
    return
  }

  loading.value = true
  try {
    // 并行请求课程信息和阶段列表
    const [stageResponse, courseResponse] = await Promise.all([
      getCourseStageListByCourseCode(courseCode),
      getCourseInfo(courseCode)
    ])
    
    if (stageResponse.code === 200) {
      stageList.value = stageResponse.data || []
      // 默认选中第一个阶段
      if (stageList.value.length > 0) {
        selectedStageIndex.value = 0
      }
    } else {
      ElMessage.error(stageResponse.msg || '获取课程阶段失败')
    }
    
    if (courseResponse.code === 200) {
      courseInfo.value = courseResponse.data
    }
    
  } catch (error) {
    console.error('获取课程阶段数据失败:', error)
    ElMessage.error('获取课程阶段数据失败')
  } finally {
    loading.value = false
  }
}

// 选择阶段（保留用于其他功能，但不再用于控制active状态）
const selectStage = (index) => {
  selectedStageIndex.value = index
}

// 获取阶段描述
const getStageDescription = (stage) => {
  if (stage.courseStageText && stage.courseStageText.length > 50) {
    return stage.courseStageText.substring(0, 50) + '...'
  }
  return stage.courseStageText || '点击查看详情'
}

// 获取阶段状态类型
const getStageStatusType = (status) => {
  switch (status) {
    case 'completed': return 'success'
    case 'active': return 'warning'
    case 'pending': return 'info'
    default: return 'info'
  }
}

// 获取阶段状态样式类（根据完成状态）
const getStageStatusClass = (stage) => {
  return stage.isCompleted === 1 ? 'status-active' : 'status-pending'
}

// 获取阶段标题头部样式类（根据完成状态）
const getStageHeaderClass = (stage) => {
  return stage.isCompleted === 1 ? 'header-active' : 'header-pending'
}

// 获取阶段状态文本
const getStageStatusText = (status) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'active': return '进行中'
    case 'pending': return '待开始'
    default: return '未知'
  }
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  return date.toLocaleString('zh-CN')
}

// 查看附件
const viewAttachment = (annex) => {
  if (annex.annexPath) {
    window.open(annex.annexPath, '_blank')
  } else {
    ElMessage.warning('附件路径无效')
  }
}

// 处理悬浮按钮点击
const handleFabClick = (item) => {
  console.log('悬浮按钮点击:', item)
}

// 生命周期
onMounted(() => {
  fetchCourseStages()
})
</script>

<style scoped>
.course-stages-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: url('@/assets/images/screen/entry-background.png') no-repeat 100% 100%;
  color: #333;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  padding: 20px 40px;
  overflow-y: auto;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* 课程信息展示 */
.course-info-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.course-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.course-title h1 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
}

.course-code {
  background: linear-gradient(135deg, #BD0407, #ff6b6b);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.course-meta {
  display: flex;
  gap: 24px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

/* 阶段容器 */
.stages-container {
  margin-top: 36px;
  border-radius: 16px;
  padding: 32px;
  backdrop-filter: blur(8px);
}

.section-title {
  text-align: center;
  margin-bottom: 40px;
}

.section-title h2 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 600;
  background: linear-gradient(135deg, #BD0407, #ff6b6b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-title p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

/* 自定义阶段时间线 */
.custom-stages-timeline {
  margin-bottom: 40px;
}

/* 阶段步骤条 */
.stages-steps {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 60px;
  position: relative;
}

.stage-step-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stage-step-wrapper:hover .stage-circle {
  transform: scale(1.1);
}

.stage-step {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  position: relative;
}

/* 阶段圆圈 */
.stage-circle {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: bold;
  color: rgba(95, 106, 121, 1);
  background: #fff;
  border: 3px solid rgba(95, 106, 121, 1);
  box-shadow: 0 10px 25px rgba(255, 151, 151, 0.25);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.stage-circle.active {
  color: rgba(189, 4, 7, 1);
  background: #fff;
  border: 3px solid rgba(189, 4, 7, 1);
  box-shadow: 0 10px 25px rgba(255, 151, 151, 0.25);
}

/* 连接线 */
.stage-line {
  position: absolute;
  top: 50%;
  left: 60%;
  width: 80%;
  height: 8px;
  background: #D4CCC4;
  transform: translateY(-50%);
  z-index: 1;
  border-radius: 4px;
}

.stage-line.active {
  background: #BD0407;
}

/* 阶段标题 */
.stage-step-wrapper .stage-title {
  margin-top: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #666;
  text-align: center;
  transition: color 0.3s ease;
}

.stage-step-wrapper.active .stage-title,
.stage-title.active {
  color: #BD0407;
}

/* 阶段内容卡片区域 */
.stages-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin-top: 40px;
}

/* 阶段内容卡片 */
.stage-content-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0px 10px 20px 0px rgba(255, 152, 152, 0.25);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.stage-content-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(255, 152, 152, 0.3);
}

.stage-content-card.selected {
  border-color: #BD0407;
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(189, 4, 7, 0.2);
}

/* 卡片头部 */
.stage-card-header {
  padding: 20px 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 活跃状态的标题头部（进行中和已完成） */
.stage-card-header.header-active {
  background: linear-gradient(180.00deg, rgba(212, 50, 42, 1),rgba(177, 36, 26, 1) 100%);
}

/* 待完成状态的标题头部 */
.stage-card-header.header-pending {
  background: linear-gradient(180.00deg, rgba(178, 179, 187, 1), rgba(95, 106, 121, 1) 100%);
}

.stage-title-text {
  font-size: 18px;
  font-weight: bold;
  color: white;
  text-align: center;
  width: 100%;
}

/* 卡片内容 */
.stage-card-content {
  padding: 24px;
  background: white;
}

.stage-card-content p {
  margin: 0;
  line-height: 1.6;
  color: rgba(67, 67, 67, 1);
}

.stage-card-content .no-content {
  color: rgba(67, 67, 67, 0.6);
  font-style: italic;
}



/* 无阶段提示 */
.no-stages {
  text-align: center;
  padding: 80px 20px;
  color: #666;
}

.no-stages i {
  font-size: 64px;
  color: #ddd;
  margin-bottom: 16px;
}

.no-stages h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #999;
}

.no-stages p {
  margin: 0;
  color: #ccc;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    padding: 20px;
    max-width: none;
  }
  
  .course-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .stages-content {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }
  
  .stages-container {
    padding: 20px;
  }
  
  .course-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .course-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .stages-steps {
    flex-direction: column;
    gap: 30px;
  }
  
  .stage-step {
    flex-direction: column;
    width: auto;
  }
  
  .stage-line {
    display: none;
  }
  
  .stages-content {
    grid-template-columns: 1fr;
  }
  
  .stage-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .stage-time-info {
    flex-direction: column;
    gap: 8px;
  }
}
</style> 