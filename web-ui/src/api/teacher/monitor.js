import request from '@/utils/request'

/**
 * 验证课程码是否有效
 * @param {string} courseCode - 课程码
 * @returns {Promise} API响应结果
 */
export function validateCourseCode(courseCode) {
  return request({
    url: `/teacher/monitor/validate-course-code/${courseCode}`,
    method: 'GET'
  })
}

/**
 * 获取监控统计信息
 * @param {string} courseCode - 课程码（可选）
 * @returns {Promise} API响应结果
 */
export function getMonitorStatistics(courseCode) {
  const params = courseCode ? { courseCode } : {}
  return request({
    url: '/teacher/monitor/statistics',
    method: 'GET',
    params
  })
}

/**
 * 获取课程信息
 * @param {string} courseCode - 课程码
 * @returns {Promise} API响应结果
 */
export function getCourseInfo(courseCode) {
  return request({
    url: `/teacher/monitor/course/${courseCode}`,
    method: 'GET'
  })
} 