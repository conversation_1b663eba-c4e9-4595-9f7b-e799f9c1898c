import request from '@/utils/request'

// 查询场景课程列表
export function listCourse() {
  return request({
    url: '/scenario/course/list',
    method: 'get'
  })
}

export function listClass(query) {
  return request({
    url: '/scenario/course/class/list',
    method: 'get',
    params: query
  })
}

// 查询场景课程详细
export function getCourse(courseId) {
  return request({
    url: '/scenario/course/' + courseId,
    method: 'get'
  })
}

// 新增场景课程
export function addCourse(data) {
  return request({
    url: '/scenario/course',
    method: 'post',
    data: data
  })
}

// 修改场景课程
export function updateCourse(data) {
  return request({
    url: '/scenario/course',
    method: 'put',
    data: data
  })
}

// 删除场景课程
export function delCourse(courseId) {
  return request({
    url: '/scenario/course/' + courseId,
    method: 'delete'
  })
}
