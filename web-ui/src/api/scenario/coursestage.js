import request from '@/utils/request'

// 查询课程阶段列表
export function listStage(query) {
  return request({
    url: '/scenario/course/stage/list',
    method: 'get',
    params: query
  })
}

// 查询课程阶段详细
export function getStage(courseStageId) {
  return request({
    url: '/scenario/course/stage/' + courseStageId,
    method: 'get'
  })
}

// 新增课程阶段
export function addStage(data) {
  return request({
    url: '/scenario/course/stage',
    method: 'post',
    data: data
  })
}

// 修改课程阶段
export function updateStage(data) {
  return request({
    url: '/scenario/course/stage',
    method: 'put',
    data: data
  })
}

// 删除课程阶段
export function delStage(courseStageId) {
  return request({
    url: '/scenario/course/stage/' + courseStageId,
    method: 'delete'
  })
}
