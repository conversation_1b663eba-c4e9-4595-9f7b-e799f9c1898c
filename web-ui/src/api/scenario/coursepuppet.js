import request from '@/utils/request'

// 查询scenario列表
export function listPuppet(query) {
  return request({
    url: '/scenario/puppet/list',
    method: 'get',
    params: query
  })
}

// 查询scenario详细
export function getCoursePuppet(puppetId) {
  return request({
    url: '/scenario/course/puppet/' + puppetId,
    method: 'get'
  })
}

// 新增scenario
export function addCoursePuppet(data) {
  return request({
    url: '/scenario/course/puppet',
    method: 'post',
    data: data
  })
}

// 修改scenario
export function updateCoursePuppet(data) {
  return request({
    url: '/scenario/course/puppet',
    method: 'put',
    data: data
  })
}

// 删除scenario
export function delCoursePuppet(puppetId) {
  return request({
    url: '/scenario/course/puppet/' + puppetId,
    method: 'delete'
  })
}
