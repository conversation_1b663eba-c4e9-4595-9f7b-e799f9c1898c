import request from '@/utils/request'

// 查询课程分组详细
export function listTeCourseGroup(courseId) {
  return request({
    url: '/scenario/course/group/te-group-list/' + courseId,
    method: 'get'
  })
}
// 查询课程分组详细
export function listCourseGroup(classCode) {
  return request({
    url: '/scenario/course/group/list/' + classCode,
    method: 'get'
  })
}

export function addCourseGroup(data) {
  return request({
    url: '/scenario/course/group',
    method: 'post',
    data: data
  })
}

export function updateCourseGroup(data) {
  return request({
    url: '/scenario/course/group',
    method: 'put',
    data: data
  })
}
export function getCourseGroup(groupId) {
  return request({
    url: '/scenario/course/group/' + groupId,
    method: 'get'
  })
}

export function delCourseGroup(groupId) {
  return request({
    url: '/scenario/course/group/' + groupId,
    method: 'delete'
  })
}
