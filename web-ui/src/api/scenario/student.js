import request from '@/utils/request'

// 查询课程内的学生列表
export function listStudent(query) {
  return request({
    url: '/scenario/student/list',
    method: 'get',
    params: query
  })
}

// 查询课程内的学生详细
export function getStudent(studentId) {
  return request({
    url: '/scenario/student/' + studentId,
    method: 'get'
  })
}

// 新增课程内的学生
export function addStudent(data) {
  return request({
    url: '/scenario/student',
    method: 'post',
    data: data
  })
}

// 修改课程内的学生
export function updateStudent(data) {
  return request({
    url: '/scenario/student',
    method: 'put',
    data: data
  })
}

// 删除课程内的学生
export function delStudent(studentId) {
  return request({
    url: '/scenario/student/' + studentId,
    method: 'delete'
  })
}
