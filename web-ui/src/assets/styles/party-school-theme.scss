// 导入ElementUI Plus基础样式
@import 'element-plus/theme-chalk/src/index';
@import './element-variables.scss';


// 自定义标签页样式
.el-tabs__header {
  border-bottom-color: $el-color-primary;
  
  .el-tabs__item {
    &:hover, &.is-active {
      color: $el-color-primary; // 悬停和选中时文字颜色改为主题红色
    }
  }
  
  .el-tabs__active-bar {
    background-color: $el-color-primary; // 选中下划线颜色改为主题红色
  }
}
// 自定义导航栏样式
.el-header {
  background-color: $el-color-primary;
  color: white;
  
  .el-menu {
    background-color: transparent;
    border: none;
    
    .el-menu-item {
      color: white;
      
      &:hover, &.is-active {
        background-color: rgba(255, 255, 255, 0.1);
        color: $el-color-primary-light-3;
      }
    }
  }
}

// 自定义卡片样式
.el-card {
  border-radius: 0.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  
  .el-card__header {
    background-color: $el-color-primary-light-9;
    border-bottom: 1px solid $el-border-color;
    font-weight: 500;
  }
}



// 自定义按钮悬停和激活效果
.el-button--primary {
  background-color: $el-color-primary;
  border-color: $el-color-primary;
  color: $el-button-text-color; // 确保主按钮文字颜色正确
  transition: background-color 0.3s, border-color 0.3s; // 添加过渡效果
  &:hover {
    // 修正：正确使用 darken 函数
    background-color: darken($el-color-primary, 5%);
    border-color: darken($el-color-primary, 5%);
  }
  
  &:active {
    background-color: darken($el-color-primary, 15%);
    border-color: darken($el-color-primary, 15%);
  }

  // 新增 is-plain 按钮样式
  &.is-plain {
    background-color: $el-color-primary-light-9; // 默认淡色背景
    border-color: $el-color-primary; // 边框颜色为主题色
    color: $el-color-primary; // 文字颜色为主题色

    &:hover {
      background-color: $el-color-primary; // 悬停时变为主题色背景
      border-color: $el-color-primary;
      color: white; // 悬停时文字变为白色
    }

    &:active {
      background-color: darken($el-color-primary, 10%); // 激活时背景加深
      border-color: darken($el-color-primary, 10%);
      color: white;
    }
  }
}

// 自定义表格样式
.el-table thead {
  th {
    background-color: $el-color-primary-light-8;
    color: $el-text-color-primary;
  }
}

// 自定义弹窗样式
.el-dialog {
  border-radius: 8px; // 添加圆角
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15); // 添加阴影
  overflow: hidden; // 隐藏溢出内容
  padding: 0px;
}


.el-dialog__header {
  background-color: $el-color-primary;
  color: white;
  padding: 16px 20px; // 按需调整标题栏内边距
  
  .el-dialog__title {
    color: white;

    font-size: 18px; 
    font-weight: 500; 
  }
  
  .el-dialog__headerbtn {
    top: 16px; 
    right: 20px; 
    

    .el-icon {
      color: white;
      font-size: 18px; 
      &:hover {
        color: $el-color-primary-light-3; 
      }
    }
  }
}






.el-dialog__body {
  padding: 20px; // 按需调整内容区域内边距
  font-size: 14px; 
  color: $el-text-color-regular; 
}







.el-dialog__footer {
  padding: 10px 20px; // 按需调整底部操作区域内边距
  text-align: right; 
  border-top: 1px solid $el-border-color; 
  background-color: #fafafa; 
}


// 自定义复选框样式
.el-checkbox__input.is-checked + .el-checkbox__label {
  color: $el-color-primary; // 复选框选中时，文字颜色改为主题红色
}

// 自定义单选按钮样式
.el-radio__input.is-checked + .el-radio__label {
  color: $el-color-primary; // 单选按钮选中时，文字颜色改为主题红色
}
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: $el-color-primary; // 选中状态背景颜色
  border-color: $el-color-primary; // 选中状态边框颜色
}

// 自定义单选按钮样式
.el-radio__input.is-checked .el-radio__inner {
  border-color: $el-color-primary; // 选中状态边框颜色
  background: $el-color-primary; // 选中状态背景颜色
}

// 自定义进度条样式
.el-progress-bar__inner {
  background-color: $el-color-primary;
}

// 自定义标签页样式
.el-tabs__header {
  border-bottom-color: $el-color-primary;
  
  .el-tabs__item {
    &:hover, &.is-active {
      color: $el-color-primary;
    }
  }
  
  .el-tabs__active-bar {
    background-color: $el-color-primary;
  }
}  


// 自定义 Tag 样式
.el-tag {
  // 基础样式
  &--primary {
    background-color: $el-color-primary-light-9;
    border-color: $el-color-primary-light-5;
    color: $el-color-primary;

    // 可关闭标签，鼠标悬停在关闭按钮上的样式
    .el-tag__close {
      &:hover {
        background-color: $el-color-primary;
        color: white;
      }
    }
  }

  // 选中状态样式
  &.is-hit {
    border-color: $el-color-primary;
  }

  // 鼠标悬停样式
  &:hover {
    border-color: $el-color-primary;
  }
}  