@import "@/assets/styles/element-variables.scss";
.top-right-btn{
  a{
    color: #93979C;
    font-size: 14px;
    margin-left: 10px;
    cursor: pointer;
    &:hover {
      color: $el-color-primary;
    }
  }  

}
.content-area {
  background-color: rgba(0, 0, 0, 0) !important;
  padding: 0 !important;
  border-radius: 0 !important;
}
.tag-box {
  background-color: #ffffff;
  margin-bottom: 10px;
  padding-left: 20px;
  .tag-scene-box.close {
    overflow: hidden;
    height: 240px;
  }
}
.tag-scene-box {
  flex-wrap: wrap;
  display: flex;
  width: calc(100% - 20px);
  .scene-box {
    display: grid;
    position: relative;
    height: 240px;
    width: 300px;
    text-align: center;
    margin-right: 10px;
    .img-title-box {
      position: relative;
      .img-wrapper {
        position: relative;
        width: 300px;
        height: 200px;
        margin-bottom: 10px;
      }
      .img-box {
        width: 100%;
        height: 100%;
        border-radius: 5px;
        cursor: pointer;
        img {
          height: 100%;
          width: 100%;
        }
      }
      /* 蒙层样式 */
      .overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.5); /* 半透明背景 */
        overflow: hidden;
        width: 100%;
        height: 0;
        transition: .5s ease; /* 动画过渡效果 */
        border-radius: 5px;
      }
      /* 鼠标悬停在 .img-wrapper 时显示蒙层 */
      .img-wrapper:hover .overlay {
        height: 100%;
      }
      /* 蒙层内容样式 */
      .overlay-content {
        color: white;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        display: flex;
        justify-content: space-around; /* 按钮左右布局 */
        padding: 0 20px;
        box-sizing: border-box;
      }
      /* 课程设置按钮样式 */
      .overlay-content .course-setting-btn {
        background-color: $el-color-primary;
        border-color: $el-color-primary;
        color: $el-button-text-color;
        border-radius: 4px;
        font-size: 14px;
        padding: 8px 16px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:hover {
          background-color: $el-color-primary-dark-2;
          border-color: $el-color-primary-dark-2;
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
      }
      /* 进入课堂按钮样式 */
      .overlay-content .enter-class-btn {
        background-color: $el-color-success;
        border-color: $el-color-success;
        color: $el-button-text-color;
        border-radius: 4px;
        font-size: 14px;
        padding: 8px 16px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:hover {
          background-color: darken($el-color-success, 10%);
          border-color: darken($el-color-success, 10%);
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
      }
    }
    .edit-box {
      display: none; /* 默认隐藏 */
      margin-top: 90px;
    }
  }
  .scene-box:hover .edit-box {
    display: block; /* 鼠标经过时显示 */
  }
}
.add-scene {
  height: 200px;
  width: 300px;
  float: left;
  border: 1px dashed rgba(221, 221, 221, 0.7);
  padding: 110px 0 0;
  line-height: 20px;
  font-size: 12px;
  text-align: center;
  color: #cbcbcb;
  background-position: center 70px;
  background-repeat: no-repeat;
  background-image: url(/src/assets/images/add.png);
  border-radius: 6px;
}