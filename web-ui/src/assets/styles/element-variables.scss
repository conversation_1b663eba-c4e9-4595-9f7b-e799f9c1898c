// ElementUI Plus 主题变量覆盖
// 主色调 - 党校经典红色，彰显庄重严肃

$el-color-primary: #b21500;
$el-color-primary-light-3: #931100;
$el-color-primary-light-5: #FF8080;
$el-color-primary-light-7: #FFB3B3;
$el-color-primary-light-8: #FFD9D9;
$el-color-primary-light-9: #FFF2F2;
$el-color-primary-dark-2: #8e1100;

// 按钮文字颜色
$el-button-text-color: #FFFFFF; // 主按钮文字设置为白色

// 辅助色 - 搭配红色，体现党校氛围
$el-color-success: #008000; // 绿色（成功）
$el-color-warning: #FFA500; // 橙色（警告）
$el-color-danger: #DC143C;  // 红色（错误）
$el-color-info: #1E90FF;    // 蓝色（信息）

// 文字颜色
$el-text-color-primary: #333333;
$el-text-color-regular: #666666;
$el-text-color-secondary: #999999;
$el-text-color-placeholder: #C0C4CC;

// 背景色
$el-bg-color: #FFFFFF;
$el-bg-color-page: #F5F5F5;
$el-bg-color-overlay: rgba(0, 0, 0, 0.7);

// 边框
$el-border-color: #E4E7ED;
$el-border-color-light: #EBEEF5;
$el-border-color-lighter: #F2F6FC;
$el-border-color-extra-light: #F5F7FA;
$el-border-color-dark: #DCDFE6;
$el-border-color-darker: #C0C4CC;

// 其他
$el-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
$el-box-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.12);
$el-box-shadow-lighter: 0 1px 2px rgba(0, 0, 0, 0.1);
$el-box-shadow-dark: 0 4px 12px rgba(0, 0, 0, 0.15);
