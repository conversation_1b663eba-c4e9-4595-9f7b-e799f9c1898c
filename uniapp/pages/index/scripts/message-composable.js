/**
 * 消息管理可组合逻辑
 */
import { ref, nextTick } from 'vue';
import { get } from '@/utils/request.js';
import { useUserStore } from '@/stores/user.js';

export function useMessages() {
    // 消息相关状态
    const messages = ref([]); // 消息列表（保留用于兼容）
    const newMessage = ref(''); // 输入框中的新消息
    const messageInput = ref(null); // 输入框引用
    const inputFocused = ref(false); // 控制输入框焦点状态
    const scrollToViewId = ref('bottom'); // 控制滚动视图滚动到底部
    const scrollTop = ref(0); // 控制滚动位置
    const currentMessages = ref([]); // 当前群组的消息

    // 分页相关状态
    const currentPage = ref(1); // 当前页码
    const pageSize = ref(20); // 每页消息数量
    const hasMoreMessages = ref(true); // 是否还有更多消息
    const isLoadingMore = ref(false); // 是否正在加载更多
    const loadMoreState = ref('hidden'); // 加载更多状态: hidden, loading, finished, error
    const lastScrollTop = ref(0); // 记录上次滚动位置

    // 滚动到消息列表底部
    const scrollToBottom = () => {
        // 如果正在加载更多消息，则不执行自动滚动到底部
        if (isLoadingMore.value) {
            console.log("[scrollToBottom] Skip scrolling during load more");
            return;
        }
        
        nextTick(() => {
            // 使用ID确保滚动到最新的消息或底部标记
            scrollToViewId.value = ''; // 先清空，再设置相同的id才能触发uni-app的滚动
            setTimeout(() => {
                // 优先滚动到最后一条消息，如果列表为空则滚动到底部标记
                const lastMsgIndex = currentMessages.value.length - 1;
                scrollToViewId.value = lastMsgIndex >= 0 ? `msg-${lastMsgIndex}` : 'bottom';
                console.log("Scrolling to:", scrollToViewId.value);
            }, 50); // 稍作延迟确保DOM更新
        });
    };

    // WebSocket 收到消息处理函数
    const handleMessageReceive = (message, currentGroupId, groupUnreadCounts) => {
        console.log("[MessageComposable] Received message:", message);
        
        // 如果是当前群组的消息，直接显示
        if (message.groupId === currentGroupId) {
            // 确保currentMessages是数组
            if (!Array.isArray(currentMessages.value)) {
                currentMessages.value = [];
            }
            
            // 改进的重复消息检测 - 首先基于ID检测，再基于内容检测
            let existingMessage = null;
            
            // 1. 如果消息有ID，优先基于ID进行去重检测
            if (message.id) {
                existingMessage = currentMessages.value.find(m => m.id === message.id);
            }
            
            // 2. 如果没有ID或者ID检测未找到重复，基于内容、发送者、时间戳进行检测
            if (!existingMessage) {
                const messageTimestamp = parseInt(message.timestamp) || 0;
                existingMessage = currentMessages.value.find(
                    m => {
                        const existingTimestamp = parseInt(m.timestamp) || 0;
                        return Math.abs(existingTimestamp - messageTimestamp) < 3000 && // 3秒内的消息
                               m.sender === message.sender && 
                               m.content === message.content &&
                               m.type === message.type;
                    }
                );
            }
            
            if (!existingMessage) {
                // 新消息应该插入到正确的时间位置
                // 由于我们的消息列表已经按时间正序排列，新消息通常应该在末尾
                // 但为了确保顺序正确，我们检查是否需要插入到中间位置
                const messageTimestamp = parseInt(message.timestamp) || 0;
                const insertIndex = currentMessages.value.findIndex(
                    m => parseInt(m.timestamp) > messageTimestamp
                );
                
                if (insertIndex === -1) {
                    // 新消息时间戳最大，追加到末尾
                    currentMessages.value = [...currentMessages.value, message];
                } else {
                    // 需要插入到指定位置保持时间顺序
                    const newMessages = [...currentMessages.value];
                    newMessages.splice(insertIndex, 0, message);
                    currentMessages.value = newMessages;
                }
                
                // 只有在不是加载历史消息时才自动滚动到底部
                if (!isLoadingMore.value) {
                    nextTick(() => {
                        scrollToBottom();
                    });
                }
            }
        } else {
            // 更新未读消息数
            if (groupUnreadCounts) {
                groupUnreadCounts[message.groupId] = 
                    (groupUnreadCounts[message.groupId] || 0) + 1;
            }
        }
    };

    // 重新focus到输入框
    const focusInput = () => {
        nextTick(() => {
            inputFocused.value = true;
        });
    };

    // 修改消息处理函数
    const handleSend = (currentGroupId, currentCourseId, groups, sendMessage) => {
        const trimmedMessage = newMessage.value.trim();
        if (!trimmedMessage) return;

        // 获取用户信息
        const userStore = useUserStore();
        const userId = userStore.userInfo?.userId;
        
        if (!userId) {
            console.error('[handleSend] userId not found in userStore');
            uni.showToast({
                title: '用户信息获取失败',
                icon: 'none'
            });
            return;
        }

        // 获取当前群组的puppet信息
        const currentGroup = groups.find(g => g.id === currentGroupId);
        const puppetName = currentGroup ? currentGroup.puppetName : '';
        const puppetIcon = currentGroup ? currentGroup.puppetIcon : '';
        
        const messageToSend = {
            type: 'CHAT',
            sender: String(userId), // 使用userId作为sender
            nickname: puppetName || userStore.nickname || userStore.username || `用户${userId}`, // 使用puppetName作为昵称
            avatar: puppetIcon || userStore.avatar || '', // 使用puppetIcon作为头像
            groupId: currentGroupId,
            courseId: currentCourseId || '', // 添加courseId字段，如果没有则使用空字符串
            content: trimmedMessage
        };
        
        sendMessage(messageToSend);
        newMessage.value = '';
        
        // 发送成功后重新focus到输入框
        focusInput();
    };

    // 加载历史消息（首次加载）
    const loadGroupMessages = async (groupId) => {
        if (!groupId) return;

        // 重置分页状态
        resetPagination();

        try {
            const response = await get(`/student/chat/messages/${groupId}`, {
                pageNum: 1,
                pageSize: pageSize.value
            });
            
            if (response.code === 200) {
                // 确保response.data是数组
                let messages = Array.isArray(response.data) ? response.data : [];
                
                // 检查是否还有更多消息
                if (messages.length < pageSize.value) {
                    hasMoreMessages.value = false;
                } else {
                    hasMoreMessages.value = true;
                }
                
                // 将接口返回的倒序数据转换为正序（按时间戳从小到大排列）
                // 这样早期的消息会显示在上面，最新的消息显示在下面
                messages.sort((a, b) => {
                    const timestampA = parseInt(a.timestamp) || 0;
                    const timestampB = parseInt(b.timestamp) || 0;
                    return timestampA - timestampB; // 正序排列
                });
                
                currentMessages.value = messages;
                
                // 滚动到底部
                nextTick(() => {
                    scrollToBottom();
                });
            }
        } catch (error) {
            console.error('获取历史消息失败:', error);
            uni.showToast({
                title: '获取历史消息失败',
                icon: 'none'
            });
        }
    };

    // 处理滚动到顶部事件
    const handleScrollToUpper = () => {
        console.log('[MessageComposable] Scroll to upper triggered');
        if (!isLoadingMore.value && hasMoreMessages.value && currentMessages.value.length > 0) {
            loadMoreMessages();
        }
    };

    // 处理滚动事件
    const handleScroll = (e) => {
        lastScrollTop.value = e.detail.scrollTop;
    };

    // 加载更多消息
    const loadMoreMessages = async (currentGroupId) => {
        if (isLoadingMore.value || !hasMoreMessages.value || !currentGroupId) {
            return;
        }
        
        console.log(`[MessageComposable] Loading more messages for group ${currentGroupId}, page ${currentPage.value + 1}`);
        
        isLoadingMore.value = true;
        loadMoreState.value = 'loading';
        
        // 记录当前滚动位置
        const currentScrollTop = lastScrollTop.value;
        
        try {
            const response = await get(`/student/chat/messages/${currentGroupId}`, {
                pageNum: currentPage.value + 1,
                pageSize: pageSize.value
            });
            
            if (response.code === 200) {
                let newMessages = Array.isArray(response.data) ? response.data : [];
                
                // 检查是否还有更多消息
                if (newMessages.length < pageSize.value) {
                    hasMoreMessages.value = false;
                    loadMoreState.value = 'finished';
                } else {
                    loadMoreState.value = 'hidden';
                }
                
                if (newMessages.length > 0) {
                    // 按时间戳正序排列新消息
                    newMessages.sort((a, b) => {
                        const timestampA = parseInt(a.timestamp) || 0;
                        const timestampB = parseInt(b.timestamp) || 0;
                        return timestampA - timestampB;
                    });
                    
                    // 记录原来第一条消息的内容，用于定位
                    const firstOldMessage = currentMessages.value.length > 0 ? currentMessages.value[0] : null;
                    
                    // 将新消息插入到当前消息列表的前面
                    currentMessages.value = [...newMessages, ...currentMessages.value];
                    
                    // 更新页码
                    currentPage.value++;
                    
                    // 使用更精确的滚动位置保持方法，避免闪烁
                    nextTick(() => {
                        if (firstOldMessage) {
                            // 计算原来第一条消息现在的索引位置
                            const targetIndex = currentMessages.value.findIndex(msg => 
                                msg.timestamp === firstOldMessage.timestamp && 
                                msg.sender === firstOldMessage.sender && 
                                msg.content === firstOldMessage.content
                            );
                            
                            if (targetIndex !== -1) {
                                // 使用延迟的scroll-into-view方式，但不重置scrollToViewId
                                // 这样可以避免先跳到底部再跳回来的问题
                                setTimeout(() => {
                                    const targetElementId = `msg-${targetIndex}`;
                                    // 直接设置目标ID，不先清空
                                    scrollToViewId.value = targetElementId;
                                    console.log(`[LoadMore] Scrolling to ${targetElementId} to maintain position`);
                                }, 50); // 减少延迟时间，提高响应速度
                            }
                        }
                    });
                    
                    console.log(`[MessageComposable] Loaded ${newMessages.length} more messages, total: ${currentMessages.value.length}`);
                } else {
                    hasMoreMessages.value = false;
                    loadMoreState.value = 'finished';
                }
            } else {
                console.error('[MessageComposable] Failed to load more messages:', response);
                loadMoreState.value = 'error';
            }
        } catch (error) {
            console.error('[MessageComposable] Error loading more messages:', error);
            loadMoreState.value = 'error';
        } finally {
            // 在finally中设置，确保加载状态正确重置
            setTimeout(() => {
                isLoadingMore.value = false;
            }, 200); // 稍微延迟重置，确保滚动完成
            
            // 如果没有更多消息了，3秒后隐藏loadmore组件
            if (!hasMoreMessages.value) {
                setTimeout(() => {
                    if (loadMoreState.value === 'finished') {
                        loadMoreState.value = 'hidden';
                    }
                }, 3000);
            }
        }
    };

    // 处理loadmore组件点击事件（用于错误状态重试）
    const handleLoadMoreClick = (currentGroupId) => {
        if (loadMoreState.value === 'error') {
            loadMoreMessages(currentGroupId);
        }
    };

    // 重置分页状态
    const resetPagination = () => {
        currentPage.value = 1;
        hasMoreMessages.value = true;
        isLoadingMore.value = false;
        loadMoreState.value = 'hidden';
    };

    return {
        // 状态
        messages,
        newMessage,
        messageInput,
        inputFocused,
        scrollToViewId,
        scrollTop,
        currentMessages,
        currentPage,
        pageSize,
        hasMoreMessages,
        isLoadingMore,
        loadMoreState,
        lastScrollTop,

        // 方法
        scrollToBottom,
        handleMessageReceive,
        focusInput,
        handleSend,
        loadGroupMessages,
        handleScrollToUpper,
        handleScroll,
        loadMoreMessages,
        handleLoadMoreClick,
        resetPagination
    };
} 