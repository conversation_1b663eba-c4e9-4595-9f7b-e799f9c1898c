<template>
  <view>
    <!-- 悬浮按钮 -->
    <wd-fab 
      v-if="!showPopup"
      position="right-bottom" 
      :expandable="false"
      :gap="fabGap"
    >
      <template #trigger>
        <view class="member-fab-trigger" @click="showMemberList">
          <wd-icon name="usergroup" size="18" color="#fff"></wd-icon>
        </view>
      </template>
    </wd-fab>

    <!-- 成员列表弹窗 -->
    <wd-popup 
      v-model="showPopup" 
      position="right" 
      @close="handlePopupClose"
      @opened="handlePopupOpened"
    >
      <view class="member-popup-container">
        <!-- 标题栏 -->
        <view class="member-header" :style="{ paddingTop: headerPaddingTop + 'px' }">
          <view class="member-title-wrapper">
            <view class="member-title">组员列表</view>
            <view class="member-count">{{ memberList.length }}人</view>
          </view>
          <view class="refresh-btn" :class="{ 'refreshing': isRefreshing }" @click="refreshMemberList">
            <wd-icon 
              name="refresh" 
              size="20" 
              color="#666" 
            />
          </view>
        </view>

        <!-- 成员列表 -->
        <view 
          class="member-list-container"
        >
          <view class="member-item" v-for="item in memberList" :key="item.userId">
            <view class="member-avatar-wrapper">
              <!-- 有头像时显示图片 -->
              <wd-img 
                v-if="item.puppetIcon || item.avatar"
                :src="item.puppetIcon || item.avatar" 
                class="member-avatar"
                :width="40"
                :height="40"
                :round="true"
                @error="() => handleAvatarError(item)"
              >
                <template #error>
                  <view class="default-avatar">
                    <wd-icon name="user" size="24" color="#333" />
                  </view>
                </template>
              </wd-img>
              
              <!-- 没有头像时显示默认图标 -->
              <view v-else class="default-avatar">
                <wd-icon name="user" size="24" color="#333" />
              </view>
            </view>
            <view class="member-info">
              <text class="member-name">
                {{ item.puppetName || item.nickName || item.userName }}
                <text v-if="item.isGroupLeader" class="admin-badge">(管理员)</text>
              </text>
            </view>
          </view>
          
          <!-- 空状态 -->
          <view v-if="memberList.length === 0 && !isLoading" class="empty-state">
            <text class="empty-text">暂无成员</text>
          </view>
          
          <!-- 加载状态 -->
          <view v-if="isLoading" class="loading-state">
            <text class="loading-text">加载中...</text>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
// 引入网络请求工具
import { get } from '@/utils/request'

// 接收父组件传递的 props
const props = defineProps({
  currentGroupId: {
    type: String,
    default: ''
  }
})

// 响应式数据
const showPopup = ref(false)
const memberList = ref([])
const statusBarHeight = ref(0)
const headerPaddingTop = ref(20)
const isRefreshing = ref(false)
const isLoading = ref(false)

// 动态计算fab按钮位置
const fabGap = computed(() => ({
  top: 0,
  left: 0,
  right: showPopup.value ? '66.67vw' : 0,
  bottom: 200
}))

// 获取系统信息
const getSystemInfo = () => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  
  // 计算标题栏顶部padding
  // 状态栏高度 + 额外间距
  headerPaddingTop.value = statusBarHeight.value + 50
}

// 显示成员列表
const showMemberList = () => {
  showPopup.value = true
  // 加载成员数据
  loadMemberList()
}

// 关闭成员列表
const closeMemberList = () => {
  showPopup.value = false
}

// 处理弹窗关闭
const handlePopupClose = () => {
  showPopup.value = false
}

// 处理弹窗打开完成
const handlePopupOpened = () => {
  // 弹窗打开完成后的处理
  console.log('成员列表弹窗已打开')
}

// 加载成员列表
const loadMemberList = async () => {
  if (!props.currentGroupId) {
    console.warn('[GroupMemberList] 当前群组ID为空，无法获取成员列表')
    uni.showToast({
      title: '群组信息缺失',
      icon: 'none'
    })
    return
  }
  
  isLoading.value = true
  
  try {
    let response
    
    // 根据groupId判断调用不同的接口
    if (props.currentGroupId === 'public') {
      // 公共聊天室，调用在线用户接口
      response = await get('/student/online/groups/public/users')
      console.log(`[GroupMemberList] 获取公共聊天室在线用户列表`)
    } else {
      // 非公共群组，调用群组成员接口
      response = await get(`/student/chat/group/${props.currentGroupId}/users`)
      console.log(`[GroupMemberList] 获取群组 ${props.currentGroupId} 成员列表`)
    }
    
    if (response.code === 200) {
      let members = []
      
      if (props.currentGroupId === 'public') {
        // 公共聊天室：response.data 是 { users: [], totalCount: number } 结构
        members = Array.isArray(response.data?.users) ? response.data.users : []
        // 对公共聊天室的GroupUserInfo数据进行字段映射，统一为GroupUserDTO格式
        members = members.map(user => ({
          userId: user.userId,
          userName: user.username,
          nickName: user.nickname,
          avatar: user.avatar,
          puppetName: '', // 公共聊天室没有马甲
          puppetIcon: user.avatar, // 公共聊天室使用真实头像作为显示头像
          isGroupLeader: false // 公共聊天室没有管理员概念
        }))
        console.log(`[GroupMemberList] 公共聊天室在线用户数量: ${response.data?.totalCount || 0}`)
      } else {
        // 非公共群组：response.data 直接是数组
        members = Array.isArray(response.data) ? response.data : []
      }
      
      // 排序：
      // 1. 首先按照puppetIndex排序（数字排序，空值排在最后）
      // 2. 然后按照管理员排在前面，普通成员排在后面
      // 3. 最后按照用户名排序
      // 注意：公共聊天室的在线用户可能没有isGroupLeader字段，需要兼容处理
      members.sort((a, b) => {
        // 首先按照puppetIndex排序
        const aIndex = a.puppetIndex ? parseInt(a.puppetIndex) : Number.MAX_SAFE_INTEGER
        const bIndex = b.puppetIndex ? parseInt(b.puppetIndex) : Number.MAX_SAFE_INTEGER
        if (aIndex !== bIndex) {
          return aIndex - bIndex
        }
        
        // 如果puppetIndex相同，则按照管理员状态排序
        if (a.isGroupLeader && !b.isGroupLeader) return -1
        if (!a.isGroupLeader && b.isGroupLeader) return 1
        
        // 如果都是管理员或都不是管理员，按照用户名排序
        const aName = a.puppetName || a.nickName || a.userName || ''
        const bName = b.puppetName || b.nickName || b.userName || ''
        return aName.localeCompare(bName)
      })
      
      memberList.value = members
      console.log(`[GroupMemberList] 成功获取成员列表，共 ${memberList.value.length} 人`)
    } else {
      console.error('[GroupMemberList] 获取成员列表失败:', response)
      memberList.value = []
      uni.showToast({
        title: response.msg || '获取成员列表失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('[GroupMemberList] 获取成员列表异常:', error)
    memberList.value = []
    uni.showToast({
      title: '获取成员列表异常',
      icon: 'error'
    })
  } finally {
    isLoading.value = false
  }
}

// 处理头像加载错误
const handleAvatarError = (item) => {
  console.log('成员头像加载失败', item)
}

// 刷新成员列表
const refreshMemberList = async () => {
  if (isRefreshing.value || isLoading.value) return // 防止重复点击
  
  isRefreshing.value = true
  
  try {
    await loadMemberList()
    
    // 显示刷新完成提示
    uni.showToast({
      title: '刷新完成',
      icon: 'success',
      duration: 1500
    })
  } catch (error) {
    console.error('[GroupMemberList] 刷新成员列表失败:', error)
  } finally {
    // 延迟停止刷新动画，让用户看到旋转效果
    setTimeout(() => {
      isRefreshing.value = false
    }, 500)
  }
}

// 组件挂载时获取系统信息
onMounted(() => {
  getSystemInfo()
})

// 暴露方法给父组件
defineExpose({
  showMemberList,
  closeMemberList,
  refreshMemberList
})
</script>

<style scoped>
/* 悬浮按钮触发器样式 */
.member-fab-trigger {
  width: 50px;
  height: 32px;
  background: #BD0407;
  border-radius: 25px 0 0 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(189, 4, 7, 0.4);
  transition: all 0.3s ease;
}

/* 弹窗容器 */
.member-popup-container {
  height: 100vh;
  width: 81.67vw;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 16px 0 0 16px;
  box-shadow: -2px 0 12px rgba(0, 0, 0, 0.1);
}

/* 标题栏 */
.member-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 12px;
  position: relative;
  transition: padding-top 0.3s ease;
}

.member-title-wrapper {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
}

.member-title {
  font-size: 18px;
  font-weight: 600;
  margin-right: 8px;
}

.member-count {
  font-size: 14px;
}

.refresh-btn {
  padding: 8px;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}


/* 刷新旋转动画 */
.refresh-btn.refreshing {
  animation: refresh-rotate 1s linear infinite;
}


@keyframes refresh-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 成员列表容器 */
.member-list-container {
  flex: 1;
  overflow-y: auto; /* 垂直滚动 */
  overflow-x: hidden; /* 隐藏水平滚动 */
  /* 自定义滚动条样式 */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent; /* Firefox */
}

/* Webkit浏览器的滚动条样式 */
.member-list-container::-webkit-scrollbar {
  width: 4px;
}

.member-list-container::-webkit-scrollbar-track {
  background: transparent;
}

.member-list-container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 2px;
}

.member-list-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.5);
}

/* 成员项样式 */
.member-item {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  position: relative;
}

.member-item:last-child {
  border-bottom: none;
}

/* 头像区域 */
.member-avatar-wrapper {
  margin-right: 12px;
}

.member-avatar {
  border-radius: 50%;
  overflow: hidden;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 成员信息 */
.member-info {
  display: flex;
  flex-direction: column;
}

.member-name {
  font-size: 16px;
  color: #333;
  margin-bottom: 2px;
  font-weight: 500;
}

.admin-badge {
  font-size: 12px;
  color: #FF6B35;
  font-weight: normal;
  margin-left: 4px;
}

.member-role {
  font-size: 12px;
  color: #999;
}

/* 在线状态 */
.member-status {
  display: flex;
  align-items: center;
}

.online-dot {
  width: 8px;
  height: 8px;
  background-color: #52c41a;
  border-radius: 50%;
  border: 1px solid #fff;
  box-shadow: 0 0 0 1px #f0f0f0;
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 16px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 加载状态 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 16px;
}

.loading-text {
  font-size: 14px;
  color: #999;
}

/* 默认头像样式 */
.default-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 