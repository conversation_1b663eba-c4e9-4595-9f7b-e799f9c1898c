.page-container {
	display: flex;
	flex-direction: column; /* 改回垂直排列 */
	height: 100vh;
	background-color: #FFFFFF;
	position: relative; /* 确保子元素定位正确 */
	
	/* 定义布局相关的CSS变量 */
	--status-top-height: calc(var(--status-bar-height, 44px) + 60px); /* 状态栏 + 胶囊按钮区域 */
	--group-list-height: 50px; /* 群组列表区域高度 */
	--input-area-height: 60px; /* 输入区域高度 */
}

/* 输入区域固定定位 */
.input-area-fixed {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 4;
	background-color: #ffffff;
	height: calc(var(--input-area-height) + env(safe-area-inset-bottom)); /* 确保高度一致 */
}

/* 顶部状态栏区域 - 与微信胶囊按钮同一行 */
.top-status-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	display: flex;
	align-items: flex-start;
	justify-content: flex-start;
	padding: calc(var(--status-bar-height) + 34px) 16px 0 16px; /* 为状态栏留出空间，并增加左右内边距 */
	z-index: 3;
	background-color: transparent; /* 透明背景，不影响小程序状态栏 */
    gap: 10px;
}

.refresh-button {
	background-color: #BD0407;
	color: white;
	padding: 2px 10px;
	border-radius: 15px;
	font-size: 11px;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 群组区域 - 为状态栏留出空间 */
.group-area {
	padding-top: var(--status-top-height); /* 为状态栏和胶囊按钮留出空间 */
	flex-shrink: 0; /* 防止群组区域被压缩 */
	height: var(--group-list-height); /* 明确设置群组区域高度 */
}

/* Group list at the top, horizontal scroll */
.group-list {
    background-color: #ffffff;
    border-bottom: 1px solid #e0e0e0;
    flex-shrink: 0; /* Don't shrink vertically */
    /* Use scroll-view for horizontal scrolling */
}

.group-scroll {
    height: auto; /* Adjust height based on content */
    white-space: nowrap; /* Keep items in one line */
    padding: 0; /* 移除padding，让tab贴合边界 */
}

.group-item {
    display: inline-block; /* Arrange items horizontally */
    padding: 12px 20px; /* 增加左右padding，减少上下padding */
    cursor: pointer;
    transition: all 0.3s ease; /* 添加过渡动画 */
    text-align: center;
    min-width: 80px; /* 减少最小宽度 */
    vertical-align: top; /* Align items to the top */
    position: relative; /* 为未读数气泡和底部横条定位 */
    border-bottom: 3px solid transparent; /* 默认透明底部边框 */
    background-color: transparent; /* 默认透明背景 */
}

.group-item:hover {
    background-color: #f8f8f8; /* 悬停效果更淡 */
}

.group-item.active {
    background-color: transparent; /* 移除背景色 */
    border-bottom: 3px solid #BD0407; /* 底部红色横条 */
    color: #BD0407; /* 文字颜色也改为红色 */
}

.group-info {
    /* Keep it simple for horizontal view */
}

.group-name {
    font-size: 15px; /* 稍微增大字体 */
    color: #333;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500; /* 增加字体粗细 */
    transition: color 0.3s ease; /* 文字颜色过渡 */
}

.group-item.active .group-name {
    color: #BD0407; /* 选中状态文字颜色 */
    font-weight: 600; /* 选中状态字体更粗 */
}

.group-meta {
    position: absolute;
    top: 8px; /* 稍微调整位置 */
    right: 8px;
}

.unread-count {
    background-color: #ff4757;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
    display: inline-block;
}

/* Chat area takes remaining space */
.chat-area {
	flex: 1; /* Take available vertical space */
	overflow: hidden;
    padding-bottom: 0;
    box-sizing: border-box;
    display: flex;
    padding: 0 10px;
    flex-direction: column;
    background-color: #F5F5F5;
}

.message-list {
	height: 100%;
    width: 100%;
    box-sizing: border-box;
}

/* 单个消息项的容器，用于间隔 */
.message-item-container {
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
}
#top, #bottom {
    height: 1px;
    overflow: hidden;
}

/* 通用消息气泡样式 */
.message-item {
	display: flex;
	align-items: flex-start;
	max-width: 100%;
	word-wrap: break-word;
    box-sizing: border-box;
    line-height: 1.4;
    font-size: 15px;
}

/* 头像样式 */
.avatar-wrapper {
    flex-shrink: 0;
    margin: 0 8px;
}

.avatar {
    border-radius: 50%;
    overflow: hidden;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 消息内容区域 */
.message-content {
    max-width: 70%;
    position: relative;
    padding: 8px 12px;
    border-radius: 10px;
    word-wrap: break-word;
    display: flex;
    flex-direction: column;
}

/* 其他用户消息样式 */
.other-message {
    justify-content: flex-start;
}

.other-message .message-content {
    background-color: #ffffff;
    color: #333333;
    border-top-left-radius: 0;
    margin-right: auto;
}

.other-message .sender {
    font-size: 12px;
    color: #888888;
    margin-bottom: 3px;
    display: block;
}

.other-message .content {
    margin-bottom: 3px;
    display: block;
}

.other-message .timestamp {
    font-size: 10px;
    color: #aaaaaa;
    align-self: flex-end;
}

/* 我的消息样式 */
.my-message {
    justify-content: flex-end;
    flex-direction: row-reverse; /* 头像在右边 */
}

.my-message .avatar-wrapper {
    margin-left: 8px;
    margin-right: 0;
}

.my-message .message-content {
    background-color: #F8E5E6;
    color: #000000;
    border-top-right-radius: 0;
    margin-left: auto;
    margin-right: 0;
}

.my-message .sender {
    font-size: 12px;
    color: #333333;
    margin-bottom: 3px;
    display: block;
    align-self: flex-start; /* 发送者名称左对齐 */
}

.my-message .content {
    margin-bottom: 3px;
    display: block;
}

.my-message .timestamp {
    font-size: 10px;
    color: #666666;
    align-self: flex-end;
}

/* 系统消息样式 */
.system-message {
    background-color: #e0e0e0;
    color: #666666;
    font-size: 12px;
    text-align: center;
    align-self: center;
    max-width: 90%;
    padding: 4px 10px;
    border-radius: 15px;
    margin-top: 5px;
    margin-bottom: 5px;
    display: block;
    width: fit-content;
    margin-left: auto;
    margin-right: auto;
}

.system-message .content {
     font-size: 12px;
     color: #666666;
}

/* Input area at the bottom */
.input-area {
	display: flex;
	align-items: center;
	padding: 8px 10px;
	background-color: #ffffff;
	border-top: 1px solid #e0e0e0;
    box-shadow: 0 -1px 3px rgba(0,0,0,0.05);
    flex-shrink: 0; /* Prevent vertical shrinking */
}

.message-input {
	flex: 1;
	height: 38px;
	padding: 0 15px;
	border: none;
	border-radius: 19px;
	margin-right: 10px;
    background-color: #f5f5f5;
    font-size: 15px;
}

.send-button {
	width: 65px;
	height: 38px;
	line-height: 38px;
	padding: 0;
    margin: 0;
	background-color: #BD0407;
	color: white;
	border: none;
	border-radius: 19px;
    font-size: 15px;
    transition: background-color 0.2s ease;
}
.send-button[disabled] {
    background-color: #c7c7cc;
    opacity: 0.7;
    cursor: not-allowed;
}

#msg-0 {
    margin-top: 15px;
}

/* 加载更多组件样式 */
.loadmore {
    padding: 10px 0;
    text-align: center;
} 