import config from './config.js'; // 引入配置文件
import storage from './storage.js'; // 引入 storage 工具

let socketTask = null; // 持有 SocketTask 实例
let messageCallback = null;
let connectionStatusCallback = null; // 用于通知连接状态变化的回调
let isConnected = false; // 可靠地跟踪连接状态
let isConnecting = false; // 跟踪是否正在尝试连接
let isManualDisconnect = false; // 标记是否是用户手动断开
let reconnectAttempts = 0; // 重连尝试次数
let reconnectTimerId = null; // 重连定时器ID
let pingIntervalId = null; // 心跳 PING 定时器 ID
let pongTimeoutId = null; // PONG 响应超时定时器 ID
let networkListener = null; // 网络状态监听器
let isNetworkAvailable = true; // 网络状态标记
let heartbeatFailCount = 0; // 心跳失败计数器

let lastConnectOptions = null; // 保存最后一次连接的选项，用于重连

// --- 常量 ---
const WS_URL = config.WEBSOCKET_URL; // 使用配置文件中的地址
const MAX_RECONNECT_ATTEMPTS = 5; // 最大重连次数
const INITIAL_RECONNECT_DELAY = 3000; // 初始重连延迟（毫秒）
const HEARTBEAT_INTERVAL = 15000; // 心跳间隔（毫秒） - 15秒
const PONG_TIMEOUT = 10000; // PONG 响应超时时间（毫秒） - 10秒
const MAX_HEARTBEAT_FAILS = 3; // 最大心跳失败次数

// 添加离线消息缓存相关的常量
const OFFLINE_MESSAGES_KEY = 'offline_messages';

// 新增：群组消息存储
const groupMessages = new Map(); // 存储每个群组的消息
const groupUnreadCounts = new Map(); // 存储每个群组的未读消息数
let currentGroupId = null; // 当前查看的群组ID

/**
 * 构建WebSocket连接URL，添加用户ID参数
 * @param {string} userId - 用户ID
 * @returns {string} 完整的WebSocket URL
 */
function buildWebSocketUrl(userId) {
    const baseUrl = WS_URL;
    if (userId) {
        const separator = baseUrl.includes('?') ? '&' : '?';
        return `${baseUrl}${separator}userId=${encodeURIComponent(userId)}`;
    }
    return baseUrl;
}

// --- 清理函数 ---
function cleanup() {
    console.log('[WebSocket] Cleaning up resources.');
    stopHeartbeat(); // 停止心跳
    clearReconnectTimer(); // 清除重连定时器
    removeNetworkListener(); // 移除网络状态监听
    if (socketTask) {
        // 移除所有监听器，避免旧监听器干扰新连接
        socketTask.onOpen(() => {});
        socketTask.onMessage(() => {});
        socketTask.onError(() => {});
        socketTask.onClose(() => {});
        // 尝试关闭，即使它可能已经关闭或失败
        socketTask.close({ code: 1000, reason: 'Cleanup initiated' });
        socketTask = null;
    }
    isConnected = false;
    isConnecting = false;
    messageCallback = null;
    connectionStatusCallback = null;
}

// 添加网络状态监听
function setupNetworkListener() {
    if (networkListener) {
        removeNetworkListener();
    }
    
    networkListener = uni.onNetworkStatusChange((res) => {
        console.log('[WebSocket] Network status changed:', res);
        isNetworkAvailable = res.isConnected;
        
        if (res.isConnected) {
            // 网络恢复，如果之前是断开状态，尝试重连
            if (!isConnected && !isConnecting && !isManualDisconnect && lastConnectOptions) {
                console.log('[WebSocket] Network restored, attempting to reconnect...');
                reconnectAttempts = 0; // 重置重连次数
                connect(lastConnectOptions, true);
            }
        } else {
            // 网络断开，停止重连尝试
            console.log('[WebSocket] Network disconnected, stopping reconnect attempts');
            clearReconnectTimer();
            if (socketTask) {
                socketTask.close({ code: 1006, reason: 'Network disconnected' });
            }
        }
    });
}

// 移除网络状态监听
function removeNetworkListener() {
    if (networkListener) {
        networkListener.off();
        networkListener = null;
    }
}

/**
 * 连接 WebSocket 服务器
 * @param {object} options - 连接选项
 * @param {string} options.username - 当前用户名
 * @param {Array} options.groups - 要加入的所有群组信息 [{id, name, courseId, puppetName, puppetIcon}, ...]
 * @param {function} options.onStatusChange - 连接状态变化时的回调 (status, reason) => {}
 * @param {function} options.onMessageReceive - 收到消息时的回调 (message) => {}
 * @param {boolean} isReconnect - 标记是否是重连尝试
 */
function connect(options = {}, isReconnect = false) {
    // 保存连接选项以便重连时使用 (仅在非重连时更新)
    if (!isReconnect) {
        lastConnectOptions = { ...options }; // 浅拷贝选项
        reconnectAttempts = 0; // 重置重连次数
        isManualDisconnect = false; // 重置手动断开标记
        setupNetworkListener(); // 设置网络状态监听
    }

    // 检查网络状态
    if (!isNetworkAvailable) {
        console.log('[WebSocket] Network unavailable, postponing connection attempt');
        return;
    }

    // 防止重复连接或在连接时再次调用
    if (socketTask || isConnecting) {
        if (isConnected) {
            console.log('[WebSocket] Already connected.');
            if (options.onStatusChange) options.onStatusChange('open');
        } else if (isConnecting) {
            console.log('[WebSocket] Connection attempt already in progress.');
        } else {
            // task 存在但未连接也未在连接中，可能状态异常，尝试清理并继续
            console.warn('[WebSocket] SocketTask exists but not connected/connecting. Cleaning up before new attempt.');
            cleanup(); // 清理旧状态
        }
        // 如果已有连接或正在连接，并且不是重连尝试，则直接返回
        if (!isReconnect && (isConnected || isConnecting)) {
            return;
        }
    }

    isConnecting = true; // 标记正在连接
    isConnected = false;
    messageCallback = options.onMessageReceive || messageCallback; // 保留旧回调或使用新回调
    connectionStatusCallback = options.onStatusChange || connectionStatusCallback;

    console.log(`[WebSocket] Attempting to connect (Attempt: ${reconnectAttempts + 1}). URL: ${WS_URL}`);
    if (connectionStatusCallback) connectionStatusCallback('connecting');

    // 清理可能存在的旧定时器
    stopHeartbeat();
    clearReconnectTimer();

    // 获取用户ID用于构建WebSocket URL
    let userId = null;
    try {
        const userInfo = storage.get('userInfo');
        if (userInfo && userInfo.userId) {
            userId = userInfo.userId;
        }
    } catch (e) {
        console.warn('[WebSocket] Failed to get user ID from storage:', e);
    }

    // 构建带用户ID参数的WebSocket URL
    const websocketUrl = buildWebSocketUrl(userId);
    console.log(`[WebSocket] Connecting to: ${websocketUrl}`);

    // 创建 SocketTask
    try {
        socketTask = uni.connectSocket({
            url: websocketUrl, // 使用带参数的URL
            header: {
                'content-type': 'application/json' // 可以根据需要添加 header
            },
            // protocols: [], // 子协议
            success: (res) => {
                console.log('[WebSocket] connectSocket task initiated successfully.', res);
                // 成功启动任务，等待 onOpen 事件
            },
            fail: (err) => {
                 console.error('[WebSocket] connectSocket task initiation failed:', err);
                 isConnecting = false; // 连接失败
                 if (connectionStatusCallback) connectionStatusCallback('error', 'Initiation failed: ' + (err.errMsg || 'Unknown error'));
                 socketTask = null; // 清理实例
                 // 尝试重连
                 handleReconnect();
            }
        });

        // --- 绑定新的事件监听器 ---

        // 监听 WebSocket 连接打开事件
        socketTask.onOpen((res) => {
            console.log('[WebSocket] Connection opened successfully.');
            isConnecting = false;
            isConnected = true;
            reconnectAttempts = 0; // 连接成功，重置重连次数
            clearReconnectTimer(); // 清除可能存在的重连定时器
            if (connectionStatusCallback) connectionStatusCallback('open');

            // 连接成功后暂不自动加入所有群组
            // 用户可以向任何有权限的群组发送消息，无需预先加入
            // JOIN/LEAVE 消息仅在用户切换群组时发送，用于业务通知
            console.log('[WebSocket] 连接已建立，用户可以向有权限的群组发送消息');

            // 尝试发送离线消息
            const offlineMessages = getOfflineMessages();
            if (offlineMessages.length > 0) {
                console.log(`[WebSocket] Attempting to send ${offlineMessages.length} offline messages`);
                offlineMessages.forEach(message => {
                    sendMessageInternal({
                        ...message,
                        isOffline: true
                    });
                });
            }

            // 启动心跳
            startHeartbeat();
        });

        // 监听 WebSocket 接收到服务器消息的事件
        socketTask.onMessage((res) => {
            // console.log('[WebSocket] Received raw message:', res.data); // 根据需要开启
            try {
                let messageData = res.data;
                if (typeof messageData !== 'string') {
                    console.warn('[WebSocket] Received non-string message, attempting to decode as UTF-8.');
                    messageData = String(messageData); // 简单处理
                }

                const message = JSON.parse(messageData);

                // 处理 PONG 消息
                if (message.type && message.type === 'PONG') {
                     // 移除PONG接收日志以减少无效打印
                     // console.debug('[WebSocket 心跳] Received PONG from server.');
                     // 清除 PONG 超时定时器
                     if (pongTimeoutId) {
                         clearTimeout(pongTimeoutId);
                         pongTimeoutId = null;
                     }
                     heartbeatFailCount = 0; // 重置心跳失败计数
                     return; // PONG 消息不传递给业务层
                }

                // 处理后端的错误消息
                if (message.type && message.type === 'ERROR') {
                     console.error(`[WebSocket] Received error message from server: ${message.content}`);
                     if (messageCallback) {
                        messageCallback({ type: 'ERROR', sender: 'System', content: message.content || 'Unknown server error', timestamp: String(Date.now()) });
                     }
                } else if (message.groupId) {
                    // 获取或创建群组消息数组
                    if (!groupMessages.has(message.groupId)) {
                        groupMessages.set(message.groupId, []);
                    }
                    
                    const groupMsgList = groupMessages.get(message.groupId);
                    
                    // 过滤JOIN和LEAVE消息，不传递给UI层展示
                    if (message.type === 'JOIN' || message.type === 'LEAVE') {
                        console.log(`[WebSocket] Received ${message.type} message but filtering from UI display:`, message.content);
                        // 仍然需要将JOIN和LEAVE消息添加到群组消息列表，但不传递给UI层
                        groupMsgList.push(message);
                        return; // 不传递给UI层
                    }
                    
                    // 对于CHAT消息，检查是否重复（基于ID和内容）
                    if (message.type === 'CHAT') {
                        // 检查消息是否有id，如果有则基于id进行去重
                        if (message.id) {
                            const existingGroupMessage = groupMsgList.find(msg => msg.id === message.id);
                            if (existingGroupMessage) {
                                console.log('[WebSocket] Received duplicate message by ID from server, skipping display:', message.content);
                                return; // 不重复传递给UI层
                            }
                        }
                        
                        // 基于内容和时间戳进行简单去重检查
                        const existingContentMessage = groupMsgList.find(msg => 
                            msg.sender === message.sender && 
                            msg.content === message.content &&
                            Math.abs(parseInt(msg.timestamp) - parseInt(message.timestamp)) < 3000 // 3秒内的消息
                        );
                        
                        if (existingContentMessage) {
                            console.log('[WebSocket] Received duplicate message by content from server, skipping display:', message.content);
                            return; // 不重复传递给UI层
                        }
                    }
                    
                    // 添加消息到对应群组（通过了重复检查的消息）
                    groupMsgList.push(message);
                    
                    // 如果不是当前查看的群组，增加未读计数
                    if (message.groupId !== currentGroupId) {
                        const currentCount = groupUnreadCounts.get(message.groupId) || 0;
                        groupUnreadCounts.set(message.groupId, currentCount + 1);
                    }
                    
                    // 其他类型的消息或非重复的CHAT消息正常传递给UI层
                    if (messageCallback) {
                        messageCallback(message);
                    }
                } else if (messageCallback) {
                     const normalizedMessage = {
                        type: message.type || 'UNKNOWN',
                        sender: message.sender || 'Anonymous',
                        groupId: message.groupId,
                        content: message.content || messageData,
                        timestamp: message.timestamp || String(Date.now())
                    };
                    
                    // 同样过滤JOIN和LEAVE消息
                    if (normalizedMessage.type === 'JOIN' || normalizedMessage.type === 'LEAVE') {
                        console.log(`[WebSocket] Received ${normalizedMessage.type} message but filtering from UI display:`, normalizedMessage.content);
                        return; // 不传递给UI层
                    }
                    
                    messageCallback(normalizedMessage);
                }
            } catch (e) {
                console.error('[WebSocket] Failed to parse message data or invalid format:', e, res.data);
                if (messageCallback) {
                     messageCallback({ type: 'ERROR', sender: 'System', content: `Invalid message format received`, timestamp: String(Date.now()) });
                }
            }
        });

        // 监听 WebSocket 错误事件
        socketTask.onError((err) => {
            console.error('[WebSocket] Connection error occurred:', err);
            isConnecting = false; // 出错，不再是连接中状态
            // isConnected 会在 onClose 中处理
            if (connectionStatusCallback) connectionStatusCallback('error', err.errMsg || 'Unknown connection error');
            // onError 发生后通常会触发 onClose，清理和重连逻辑放在 onClose 中
            // 不需要手动 close 或 nullify socketTask
        });

        // 监听 WebSocket 连接关闭事件
        socketTask.onClose((res) => {
            console.log(`[WebSocket] Connection closed. Code: ${res.code}, Reason: ${res.reason}`);
            const wasConnected = isConnected; // 记录关闭前是否曾连接成功
            isConnected = false;
            isConnecting = false;
            stopHeartbeat(); // 停止心跳
            // 只有在连接曾经成功建立（wasConnected）或者非用户手动断开时才通知外部 'close'
            // 避免在连接尝试失败（从未 onOpen）时重复通知错误和关闭
            if (wasConnected || !isManualDisconnect) {
                if (connectionStatusCallback) connectionStatusCallback('close', { code: res.code, reason: res.reason });
            }

            socketTask = null; // 清理 task 实例

            // 如果不是手动断开，尝试重连
            if (!isManualDisconnect) {
                console.log('[WebSocket] Connection closed unexpectedly. Attempting to reconnect...');
                handleReconnect();
            } else {
                 console.log('[WebSocket] Connection closed manually.');
                 // 手动断开，彻底清理
                 cleanup(); // 清理所有资源和回调
            }
            // 重置手动断开标记，以防万一
            isManualDisconnect = false;
        });

    } catch (error) {
         console.error('[WebSocket] Error during uni.connectSocket or event listener setup:', error);
         isConnecting = false;
         if (connectionStatusCallback) connectionStatusCallback('error', 'Setup failed: ' + error.message);
         socketTask = null;
         handleReconnect(); // 尝试重连
    }
}

// 内部发送消息，不检查 isConnected，主要供内部逻辑（如JOIN, PING）使用
function sendMessageInternal(message) {
    if (socketTask) {
        if (!message.timestamp) {
            message.timestamp = String(Date.now());
        }
        const payload = JSON.stringify(message);

        socketTask.send({
            data: payload,
            success: () => {
                // 过滤掉ping/pong消息的日志打印
                if (message.type !== 'PING' && message.type !== 'PONG') {
                    console.log('[WebSocket] Message sent successfully:', message.id);
                }
                // 如果消息是从离线缓存发送的，则从缓存中移除
                if (message.isOffline) {
                    removeOfflineMessage(message.id);
                }
                
                // 对于CHAT消息，不再立即显示，而是等待服务器回显
                // 这样可以避免消息重复显示的问题
                if (message.type === 'CHAT') {
                    console.log('[WebSocket] CHAT message sent successfully, waiting for server echo:', message.id);
                    // 可以在这里通过某种方式通知UI层消息发送状态，但不显示消息内容
                }
            },
            fail: (err) => {
                console.error('[WebSocket] Failed to send message:', err);
                // 发送失败时保存到离线缓存
                saveOfflineMessage(message);
                // 通知UI层消息发送失败
                if (messageCallback) {
                    messageCallback({
                        ...message,
                        sendStatus: 'failed',
                        isOffline: true
                    });
                }
            }
        });
    } else {
        console.error('[WebSocket] Cannot send message, no socket task.');
        // 保存到离线缓存
        saveOfflineMessage(message);
        // 通知UI层消息已保存到离线缓存
        if (messageCallback) {
            messageCallback({
                ...message,
                sendStatus: 'pending',
                isOffline: true
            });
        }
    }
}

/**
 * 通过 WebSocket 发送消息 (供外部调用)
 * @param {object} message - 要发送的消息对象 (需要包含 type, sender, groupId, content)
 */
function sendMessage(message) {
    // 为消息添加唯一ID
    if (!message.id) {
        message.id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    }

    // 检查连接状态
    if (socketTask && isConnected) {
        sendMessageInternal(message);
    } else {
        console.log('[WebSocket] Connection not available, saving message to offline cache');
        saveOfflineMessage(message);
        // 通知UI层消息已保存到离线缓存
        if (messageCallback) {
            messageCallback({
                ...message,
                sendStatus: 'pending',
                isOffline: true
            });
        }
    }
}

/**
 * 断开 WebSocket 连接
 */
function disconnect() {
     console.log('[WebSocket] User requested disconnect.');
     isManualDisconnect = true; // 标记为手动断开
     stopHeartbeat(); // 停止心跳
     clearReconnectTimer(); // 取消任何待处理的重连

    // 检查 socketTask 是否存在
    if (socketTask) {
        // 尝试发送所有群组的 LEAVE 消息（如果连接仍然有效）
        const groups = lastConnectOptions?.groups || [];
        if (isConnected && groups.length > 0) {
            console.log('[WebSocket] Sending LEAVE messages for all groups before closing.');
            
            // 从localStorage获取用户信息
            let userId = null;
            let defaultDisplayName = lastConnectOptions?.username || 'Anonymous';
            let defaultUserAvatar = '';
            try {
                const userInfo = storage.get('userInfo');
                if (userInfo) {
                    userId = userInfo.userId;
                    if (userInfo.nickname || userInfo.NICKNAME) {
                        defaultDisplayName = userInfo.nickname || userInfo.NICKNAME;
                    } else if (userInfo.username || userInfo.USERNAME) {
                        defaultDisplayName = userInfo.username || userInfo.USERNAME;
                    }
                    if (userInfo.avatar || userInfo.AVATAR) {
                        defaultUserAvatar = userInfo.avatar || userInfo.AVATAR;
                    }
                }
            } catch (e) {
                console.warn('[WebSocket] Failed to get user info for LEAVE messages, using fallback:', e);
            }
            
            // 使用userId作为sender，如果没有则使用username作为备选
            const senderIdentifier = userId ? String(userId) : lastConnectOptions?.username;
            
            if (senderIdentifier) {
                // 为每个群组发送LEAVE消息
                groups.forEach(group => {
                    // 使用群组特定的昵称和头像，如果没有则使用默认值
                    const displayName = group.puppetName || defaultDisplayName;
                    const userAvatar = group.puppetIcon || defaultUserAvatar;
                    
                    sendMessageInternal({
                        type: 'LEAVE',
                        sender: senderIdentifier,
                        nickname: displayName,
                        avatar: userAvatar,
                        groupId: group.id,
                        courseId: group.courseId || '',
                        content: `${displayName}退出了群组${group.name}`
                    });
                });
            }
             // LEAVE 消息发送是异步的，这里不等待，直接关闭
        }

         // 使用 socketTask.close 方法
         console.log('[WebSocket] Initiating close command...');
         socketTask.close({
            code: 1000, // 正常关闭
            reason: 'User initiated disconnect',
            success: () => console.log('[WebSocket] Close command initiated successfully.'),
            fail: (err) => console.error('[WebSocket] Failed to initiate close command:', err)
         });
         // 实际的清理工作 (socketTask = null, callbacks = null) 由 onClose 事件处理

    } else {
        console.log('[WebSocket] No active connection task to disconnect.');
        // 如果没有 task，也需要确保清理回调等
        cleanup();
    }
}

// --- 心跳机制 ---

function startHeartbeat() {
    console.log('[WebSocket 心跳] Starting heartbeat mechanism.');
    stopHeartbeat(); // 先停止可能存在的旧心跳
    pingIntervalId = setInterval(() => {
        sendPing();
    }, HEARTBEAT_INTERVAL);
    // 立即发送一次 PING 以快速检测连接
    // sendPing(); // 可选：立即发送一次
}

function stopHeartbeat() {
    if (pingIntervalId) {
        clearInterval(pingIntervalId);
        pingIntervalId = null;
        console.log('[WebSocket 心跳] Heartbeat stopped.');
    }
    if (pongTimeoutId) {
        clearTimeout(pongTimeoutId);
        pongTimeoutId = null;
        // console.debug('[WebSocket 心跳] PONG timeout cleared due to heartbeat stop.'); // 调试信息
    }
}

function sendPing() {
    if (socketTask && isConnected) {
         // 移除PING发送日志以减少无效打印
         // console.debug('[WebSocket 心跳] Sending PING.');
         sendMessageInternal({ 
             type: 'PING',
             courseId: '' // PING消息使用空字符串作为courseId
         });

         // 设置 PONG 超时定时器
         if (pongTimeoutId) clearTimeout(pongTimeoutId); // 清除上一个超时
         pongTimeoutId = setTimeout(() => {
             heartbeatFailCount++; // 增加心跳失败计数
             console.warn(`[WebSocket 心跳] PONG timeout after ${PONG_TIMEOUT}ms. Fail count: ${heartbeatFailCount}/${MAX_HEARTBEAT_FAILS}`);
             
             if (heartbeatFailCount >= MAX_HEARTBEAT_FAILS) {
                 console.error(`[WebSocket 心跳] ${MAX_HEARTBEAT_FAILS} consecutive heartbeat failures. Assuming connection lost.`);
                 // 关闭连接，触发 onClose 中的重连逻辑
                 if (socketTask) {
                     socketTask.close({ code: 1006, reason: 'Multiple heartbeat failures' }); // 1006: Abnormal Closure
                 }
                 heartbeatFailCount = 0; // 重置计数器
             }
             // 清理 pongTimeoutId
             pongTimeoutId = null;
         }, PONG_TIMEOUT);
    } else {
         console.warn('[WebSocket 心跳] Cannot send PING, WebSocket not connected.');
    }
}

// --- 断线重连机制 ---

function handleReconnect() {
    stopHeartbeat(); // 确保心跳停止
    
    // 检查网络状态
    if (!isNetworkAvailable) {
        console.log('[WebSocket] Network unavailable, postponing reconnect attempt');
        return;
    }
    
    if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
        console.error(`[WebSocket] Maximum reconnect attempts (${MAX_RECONNECT_ATTEMPTS}) reached. Giving up.`);
        if (connectionStatusCallback) connectionStatusCallback('error', 'Reconnect failed: Max attempts reached');
        cleanup(); // 彻底清理
        return;
    }

    // 计算本次重连延迟（指数退避）
    const delay = INITIAL_RECONNECT_DELAY * Math.pow(2, reconnectAttempts);
    console.log(`[WebSocket] Will attempt reconnect #${reconnectAttempts + 1} in ${delay / 1000} seconds.`);

    clearReconnectTimer(); // 清除可能存在的旧定时器

    reconnectTimerId = setTimeout(() => {
        reconnectAttempts++;
        if (lastConnectOptions) {
            console.log(`[WebSocket] Retrying connection (Attempt: ${reconnectAttempts})...`);
            connect(lastConnectOptions, true); // 使用保存的选项进行重连
        } else {
             console.error('[WebSocket] Cannot reconnect, no previous connection options available.');
             cleanup(); // 无法重连，清理
        }
    }, delay);
}

function clearReconnectTimer() {
    if (reconnectTimerId) {
        clearTimeout(reconnectTimerId);
        reconnectTimerId = null;
    }
}

// 添加离线消息缓存相关的方法
function saveOfflineMessage(message) {
    try {
        const offlineMessages = storage.get(OFFLINE_MESSAGES_KEY, []);
        // 为消息添加发送状态标记
        message.sendStatus = 'pending';
        message.timestamp = message.timestamp || String(Date.now());
        offlineMessages.push(message);
        storage.set(OFFLINE_MESSAGES_KEY, offlineMessages);
        console.log('[WebSocket] Message saved to offline cache:', message);
    } catch (e) {
        console.error('[WebSocket] Failed to save offline message:', e);
    }
}

function removeOfflineMessage(messageId) {
    try {
        const offlineMessages = storage.get(OFFLINE_MESSAGES_KEY, []);
        const updatedMessages = offlineMessages.filter(msg => msg.id !== messageId);
        storage.set(OFFLINE_MESSAGES_KEY, updatedMessages);
        console.log('[WebSocket] Message removed from offline cache:', messageId);
    } catch (e) {
        console.error('[WebSocket] Failed to remove offline message:', e);
    }
}

function getOfflineMessages() {
    return storage.get(OFFLINE_MESSAGES_KEY, []);
}

// 新增：获取指定群组的消息
function getGroupMessages(groupId) {
    return groupMessages.get(groupId) || [];
}

// 新增：获取指定群组的未读消息数
function getGroupUnreadCount(groupId) {
    return groupUnreadCounts.get(groupId) || 0;
}

// 新增：标记群组消息为已读
function markGroupAsRead(groupId) {
    groupUnreadCounts.set(groupId, 0);
}

// 新增：设置当前查看的群组ID
function setCurrentGroupId(groupId) {
    currentGroupId = groupId;
    // 切换群组时自动标记为已读
    markGroupAsRead(groupId);
}

// 导出公共方法
export { 
    connect, 
    sendMessage, 
    disconnect, 
    getOfflineMessages,
    getGroupMessages,
    getGroupUnreadCount,
    markGroupAsRead,
    setCurrentGroupId
}; 