
# 项目分析：情景课堂

## 1. 业务概述

本项目名为“情景课堂”，是一个基于若依（RuoYi-Vue）框架开发的互动教学管理系统。从目录结构和依赖分析，该系统包含以下几个主要部分：

*   **后台管理系统**：一个功能完善的Web后台，用于管理系统的核心数据，如用户、角色、课程、任务等。
*   **移动端应用**：一个使用`uniapp`开发的移动端，可能是小程序或H5应用，面向教师和学生，提供课程互动、问卷调查、任务提交等功能。

根据`documents`目录下的文件名推测，项目重点功能可能包括：

*   **问卷系统**：支持在线问卷、答题和数据统计。
*   **选课功能**：学生可选修课程。
*   **任务/作业**：教师可以发布任务，学生可以查看和提交。
*   **WebSocket应用**：用于实现实时互动功能，如即时通讯、状态同步等。

## 2. 技术栈

### 2.1. 后端 (Java)

*   **核心框架**: Spring Boot 2.5.15
*   **安全框架**: Spring Security + JWT
*   **数据访问**: MyBatis Plus, PageHelper
*   **数据库**: 达梦数据库 (DM8)
*   **数据库连接池**: Druid
*   **缓存**: Redis
*   **Websocket**: Spring Boot Starter WebSocket
*   **其他**: Hutool, Fastjson2, Lombok, Quartz (定时任务)

### 2.2. 后台管理前端 (`web-ui`)

*   **核心框架**: Vue.js 3
*   **UI框架**: Element Plus
*   **构建工具**: Vite
*   **状态管理**: Pinia
*   **路由**: Vue Router
*   **图表**: Echarts

### 2.3. 移动端 (`uniapp`)

*   **框架**: uni-app
*   **UI组件库**: wot-design-uni
*   **分页组件**: z-paging

## 3. 代码结构

*   `scenario-class/`
    *   `src/main/java/com/ruoyi/`: 后端Java源代码。
        *   `common`: 通用工具类、常量、枚举等。
        *   `framework`: 框架核心配置，如安全、MyBatis、Redis配置。
        *   `project`: 具体的业务模块代码。
            *   `system`: 若依自带的系统管理功能（用户、角色、菜单等）。
            *   `scenarioclass` (推测): “情景课堂”的核心业务代码。
    *   `src/main/resources/`: 资源文件。
        *   `mapper/`: MyBatis的XML映射文件。
        *   `static/`: 静态资源。
        *   `application.yml`: 主配置文件。
    *   `web-ui/`: PC后台管理前端。
        *   `src/`: 前端源代码。
            *   `api/`: API请求定义。
            *   `views/`: 页面组件。
            *   `components/`: 可复用的UI组件。
            *   `store/`: Pinia状态管理。
            *   `router/`: 路由配置。
    *   `uniapp/`: 移动端项目。
        *   `pages/`: 页面。
            *   `survey/`: 问卷页面。
            *   `elective/`: 选课页面。
            *   `task/`: 任务页面。
        *   `components/`: 可复用组件。
        *   `stores/`: 状态管理 (可能是Pinia或Vuex)。
        *   `utils/`: 工具函数，如`request.js`封装了HTTP请求。
    *   `sql/`: 存放数据库初始化和升级脚本。
    *   `documents/`: 存放项目相关的需求和设计文档。

## 4. 关键配置文件

*   `pom.xml`: 定义了后端项目的Maven依赖和构建配置。
*   `web-ui/package.json`: 定义了PC前端项目的NPM依赖和脚本。
*   `uniapp/package.json`: 定义了移动端项目的NPM依赖。
*   `src/main/resources/application.yml`: Spring Boot主配置文件，包含了数据库、Redis、服务器端口等核心配置。
*   `src/main/resources/application-druid.yml`: Druid数据源的详细配置。

