# 问卷系统表关系分析与修复

## 问题描述

在小程序的 `/student/chat/currentUser/courseInfo` 接口中，返回的用户分组信息中 `sceneId` 字段始终为 `null`，导致小程序主页无法正确显示用户需要填写的问卷（问卷通过 `sceneId` 关联）。

## 数据库表关系分析

### 核心表结构

根据代码分析，情景课堂系统涉及以下主要表：

#### 1. 用户相关表
- **SYS_USER**: 系统用户表，存储用户基本信息

#### 2. 场景相关表
- **DC_SCENE**: 场景模板表，存储场景基本信息
  - `scene_id`: 场景ID（主键）
  - `scene_name`: 场景名称
  - `scene_introduction`: 场景描述

- **DC_SCENE_GROUP**: 场景分组模板表，定义场景的分组结构
  - `group_id`: 分组ID（主键）
  - `scene_id`: 关联的场景ID
  - `group_name`: 分组名称

#### 3. 课程相关表
- **DC_COURSE**: 课程表，存储课程基本信息
  - `course_id`: 课程ID（主键）
  - `course_name`: 课程名称
  - `scene_id`: 关联的场景模板ID
  - `course_introduction`: 课程介绍

- **DC_COURSE_GROUP**: 课程分组表，基于场景模板创建的实际课程分组
  - `group_id`: 课程分组ID（主键）
  - `scene_id`: 关联的场景模板ID
  - `group_name`: 课程分组名称

- **DC_COURSE_STUDENT**: 课程学生表，记录学生与课程分组的关系
  - `student_code`: 学生代码（关联用户表）
  - `group_id`: 课程分组ID
  - `puppet_name`: 马甲名称
  - `puppet_icon`: 马甲图标

#### 4. 问卷相关表
- **DC_SCENE_TESTPAPER**: 问卷调查表，问卷属于场景级别
  - `paper_id`: 问卷ID（主键）
  - `scene_id`: 关联的场景ID
  - `paper_title`: 问卷标题

### 表关系图

```mermaid
graph TD
    A[SYS_USER<br/>用户表] -->|LEFT JOIN| B[DC_COURSE_STUDENT<br/>课程学生表]
    B -->|LEFT JOIN| C[DC_COURSE_GROUP<br/>课程分组表]
    C -->|LEFT JOIN| D[DC_COURSE<br/>课程表]
    D -->|LEFT JOIN| E[DC_SCENE<br/>场景表]
    F[DC_SCENE_TESTPAPER<br/>问卷表] -->|scene_id| E
    G[DC_SCENE_GROUP<br/>场景分组模板] -->|scene_id| E
    
    style E fill:#ccffcc
    style F fill:#ffcccc
```

## 问题原因分析

### 原始错误的SQL查询

在 `src/main/resources/mybatis/student/StSysUserMapper.xml` 中的原始查询：

```sql
SELECT 
    u.USER_NAME AS userName,
    u.NICK_NAME AS nickName,
    u.AVATAR AS avatar,
    c_group.GROUP_ID AS groupId,
    c_group.GROUP_NAME AS groupName,
    c_student.PUPPET_NAME AS puppetName,
    c_student.PUPPET_ICON AS puppetIcon,
    course.course_id AS courseId,        -- 错误：字段名不正确
    course.COURSE_NAME AS courseName,    -- 可能缺少相关的实体类和Mapper
    scene.scene_id AS sceneId,
    scene.SCENE_NAME AS sceneName,
    scene.SCENE_INTRODUCTION AS sceneIntroduction
FROM 
    SYS_USER u
LEFT JOIN DC_COURSE_STUDENT c_student
    ON u.USER_NAME = c_student.STUDENT_CODE
LEFT JOIN DC_COURSE_GROUP c_group
    ON c_student.GROUP_ID = c_group.GROUP_ID
LEFT JOIN DC_COURSE course                    -- 表存在但缺少实体类和Mapper
    ON c_group.SCENE_ID = course.SCENE_ID
LEFT JOIN DC_SCENE scene
    ON course.SCENE_ID = scene.SCENE_ID
```

### 问题分析

1. **缺少实体类和Mapper**: `DC_COURSE` 表在数据库中存在，但项目中缺少对应的实体类和Mapper接口，导致MyBatis无法正确映射。

2. **字段名错误**: 查询中使用的是 `course.course_id`，但实际数据库字段应该是 `COURSE_ID`。

3. **关联关系正确**: 表关联逻辑实际上是正确的：
   - `DC_COURSE_GROUP.SCENE_ID` 关联 `DC_COURSE.SCENE_ID`
   - `DC_COURSE.SCENE_ID` 关联 `DC_SCENE.SCENE_ID`

4. **MyBatis映射失败**: 由于缺少实体类，MyBatis无法正确处理表关联，导致 `sceneId` 返回 `null`

## 修复方案

### 修复后的SQL查询

```sql
SELECT 
    u.USER_NAME AS userName,
    u.NICK_NAME AS nickName,
    u.AVATAR AS avatar,
    c_group.GROUP_ID AS groupId,
    c_group.GROUP_NAME AS groupName,
    c_student.PUPPET_NAME AS puppetName,
    c_student.PUPPET_ICON AS puppetIcon,
    course.COURSE_ID AS courseId,           -- 修复：使用正确的字段名
    course.COURSE_NAME AS courseName,       -- 修复：添加了对应的实体类和Mapper
    scene.scene_id AS sceneId,              -- 现在可以正确获取
    scene.SCENE_NAME AS sceneName,
    scene.SCENE_INTRODUCTION AS sceneIntroduction
FROM 
    SYS_USER u
LEFT JOIN DC_COURSE_STUDENT c_student
    ON u.USER_NAME = c_student.STUDENT_CODE
LEFT JOIN DC_COURSE_GROUP c_group
    ON c_student.GROUP_ID = c_group.GROUP_ID
LEFT JOIN DC_COURSE course                  -- 修复：添加了实体类和Mapper支持
    ON c_group.SCENE_ID = course.SCENE_ID
LEFT JOIN DC_SCENE scene
    ON course.SCENE_ID = scene.SCENE_ID
```

### 修复原理

1. **创建缺失的实体类**: 为 `DC_COURSE`、`DC_COURSE_GROUP`、`DC_COURSE_STUDENT` 表创建了对应的MyBatis Plus实体类

2. **创建对应的Mapper接口**: 为上述实体类创建了继承 `BaseMapper` 的Mapper接口

3. **修正字段映射**: 
   - 将 `course.course_id` 修正为 `course.COURSE_ID`
   - 确保所有字段名与数据库表结构一致

4. **保持正确的关联链**: 维护完整的表关联关系：
   - `SYS_USER` → `DC_COURSE_STUDENT` → `DC_COURSE_GROUP` → `DC_COURSE` → `DC_SCENE`

## 两种分组的关系说明

### DC_SCENE_GROUP (场景分组模板)
- **概念**: 场景设计阶段定义的分组模板
- **作用**: 相当于分组的"蓝图"
- **用途**: 为创建具体课程时提供分组参考

### DC_COURSE_GROUP (课程分组)
- **概念**: 基于场景模板创建的实际运行分组
- **作用**: 真正的学生分组，学生被分配到这些组中
- **关联**: 通过 `SCENE_ID` 关联到对应的场景模板

### 问卷与分组的关系

```mermaid
graph LR
    A[学生] --> B[DC_COURSE_GROUP<br/>课程分组]
    B --> C[DC_COURSE<br/>课程]
    C --> D[DC_SCENE<br/>场景模板] 
    D --> E[DC_SCENE_TESTPAPER<br/>问卷]
    
    style D fill:#ccffcc
    style E fill:#ffcccc
```

1. **学生** 被分配到 **课程分组**
2. **课程分组** 关联到 **课程**
3. **课程** 关联到 **场景模板**
4. **问卷** 属于 **场景模板** 级别
5. 通过场景ID，学生可以访问对应场景下的问卷

## 验证步骤

### 1. 检查数据库连接
确保 `DC_COURSE_GROUP` 表中的数据包含正确的 `SCENE_ID`

### 2. 检查课程数据
确保 `DC_COURSE` 表中存在对应的课程记录，且 `SCENE_ID` 字段正确

### 3. 检查场景数据
确保 `DC_SCENE` 表中存在对应的场景记录

### 4. 测试接口
调用 `/student/chat/currentUser/courseInfo` 接口，验证返回的 `sceneId` 不再为 `null`

### 5. 验证问卷显示
在小程序端验证能否根据 `sceneId` 正确显示对应的问卷

## 预期结果

修复后，`/student/chat/currentUser/courseInfo` 接口应该返回类似以下结构的数据：

```json
{
  "code": 200,
  "data": [
    {
      "userName": "student001",
      "nickName": "张三",
      "groupId": "GROUP_001",
      "groupName": "第一组",
      "courseId": "COURSE_001",
      "courseName": "台风应急演练课程",
      "sceneId": "SCENE_001",    // 不再为null
      "sceneName": "台风应急演练",
      "sceneIntroduction": "台风应急演练场景描述"
    }
  ]
}
```

通过 `sceneId`，小程序可以查询 `DC_SCENE_TESTPAPER` 表获取对应的问卷信息。

## 创建的实体类和Mapper

为了支持正确的表关联，本次修复创建了以下文件：

### 实体类
1. **DcCourse.java** - `src/main/java/com/ruoyi/project/scenario/domain/DcCourse.java`
   - 对应 `DC_COURSE` 表
   - 使用 MyBatis Plus 注解
   - 主键使用雪花ID策略

2. **DcCourseGroup.java** - `src/main/java/com/ruoyi/project/scenario/domain/DcCourseGroup.java`
   - 对应 `DC_COURSE_GROUP` 表
   - 使用 MyBatis Plus 注解
   - 主键使用雪花ID策略

3. **DcCourseStudent.java** - `src/main/java/com/ruoyi/project/scenario/domain/DcCourseStudent.java`
   - 对应 `DC_COURSE_STUDENT` 表
   - 使用 MyBatis Plus 注解
   - 主键使用雪花ID策略

4. **DcCoursePaperHistory.java** - `src/main/java/com/ruoyi/project/scenario/domain/DcCoursePaperHistory.java`
   - 对应 `DC_COURSE_PAPER_HISTORY` 表
   - 使用 MyBatis Plus 注解
   - 主键使用雪花ID策略

5. **DcCourseQuestionHistory.java** - `src/main/java/com/ruoyi/project/scenario/domain/DcCourseQuestionHistory.java`
   - 对应 `DC_COURSE_QUESTION_HISTORY` 表
   - 使用 MyBatis Plus 注解
   - 主键使用雪花ID策略

### Mapper接口
1. **DcCourseMapper.java** - `src/main/java/com/ruoyi/project/scenario/mapper/DcCourseMapper.java`
2. **DcCourseGroupMapper.java** - `src/main/java/com/ruoyi/project/scenario/mapper/DcCourseGroupMapper.java`
3. **DcCourseStudentMapper.java** - `src/main/java/com/ruoyi/project/scenario/mapper/DcCourseStudentMapper.java`
4. **DcCoursePaperHistoryMapper.java** - `src/main/java/com/ruoyi/project/scenario/mapper/DcCoursePaperHistoryMapper.java`
5. **DcCourseQuestionHistoryMapper.java** - `src/main/java/com/ruoyi/project/scenario/mapper/DcCourseQuestionHistoryMapper.java`

所有Mapper接口都继承了 `BaseMapper<T>`，提供基础的CRUD操作。 