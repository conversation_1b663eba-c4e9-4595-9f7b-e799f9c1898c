---
date: 2025年5月5日
aliases:
  - 
---
#### 核心业务实体及关系图

```
用户体系
├─ 用户信息 (SYS_USER) —— 基础用户数据，含教职工和学生
└─ 学员信息 (ST_STUDENT_INFO) —— 关联学号(STUDENT_CODE)，扩展学生详细信息

班级管理
├─ 班级 (TE_CLASS_INFO) —— 定义班级属性，如班级代码(CLASS_CODE)、时间、类型等
└─ 班级分组 (TE_CLASSGROUP_INFO) —— 班级内的小组划分，通过GROUP_CLASSCODE关联班级

场景模板
├─ 场景 (DC_SCENE) —— 教学场景模板，包含阶段、任务、试题等配置
│   ├─ 场景阶段 (DC_SCENE_STAGE) —— 划分场景的教学步骤，按顺序执行
│   │   └─ 阶段附件 (DC_SCENE_STAGE_ANNEX) —— 存储阶段相关文件（如PDF、视频）
│   ├─ 场景分组 (DC_SCENE_GROUP) —— 定义场景中的角色分组（如讨论组）
│   │   └─ 马甲 (DC_SCENE_PUPPET) —— 分组成员角色，含头像、名称等属性
│   ├─ 任务模板 (DC_SCENE_TASK) —— 场景中的预设任务（如小组作业）
│   └─ 试卷模板 (DC_SCENE_TESTPAPER) 
│       └─ 试题 (DC_SCENE_QUESTION) 
│           └─ 试题选项 (DC_SCENE_QUESTION_ITEM) —— 选择题的选项及正确答案标识(IS_RIGHT)

课程实例
└─ 课程 (DC_COURSE) —— 基于场景创建的实例课程
    ├─ 课程阶段 (DC_COURSE_STAGE) —— 实例化场景阶段，可调整内容
    │   └─ 阶段附件 (DC_COURSE_STAGE_ANNEX) —— 课程特有的附件
    ├─ 课程分组 (DC_COURSE_GROUP) —— 继承或复制场景分组结构（通过SCENE_ID关联）
    ├─ 学生分配 (DC_COURSE_STUDENT) 
    │   ├─ 关联学员信息 (STUDENT_CODE → ST_STUDENT_INFO)
    │   └─ 分配至课程分组 (GROUP_ID → DC_COURSE_GROUP)
    ├─ 聊天记录 (DC_COURSE_CHAT_HISTORY) —— 按课程和分组记录讨论内容
    ├─ 任务记录 (DC_COURSE_TASK_HISTORY) —— 学生提交的任务结果
    └─ 测评记录 
        ├─ 试卷下发 (DC_COURSE_PAPER_HISTORY) —— 关联试卷模板
        └─ 学生答题 (DC_COURSE_QUESTION_HISTORY) —— 记录每道题的答案
```

#### 关键关联说明

1. **场景与课程**
    
    - **场景** 作为模板，定义教学流程（阶段、任务、试题）。
        
    - **课程** 通过 `SCENE_ID` 引用场景，生成实际教学实例，允许自定义阶段内容和附件。
        
2. **分组体系**
    
    - **场景分组** 预设角色分组（如辩论正反方），包含马甲角色。
        
    - **课程分组** 继承场景分组结构（通过 `SCENE_ID` 关联），将实际学生（来自班级）分配到对应分组，并指定组长 (`IS_GROUP_LEADER`)。
        
3. **测评流程**
    
    - **试卷模板** 在场景中设计，包含试题及选项。
        
    - **课程实例** 下发给学生后，记录答题结果 (`DC_COURSE_QUESTION_HISTORY`)，关联到具体试卷实例 (`PH_ID`)。
        
4. **用户与数据流**
    
    - 学生通过 `STUDENT_CODE` 关联班级 (`TE_CLASS_INFO`) 和课程实例中的分组。
        
    - 教师或管理员通过 `CREATE_USER` 字段跟踪操作记录。
        

#### 分组依赖

- **课程分组 (DC_COURSE_GROUP)** 依赖：
    
    - 所属课程 (`SCENE_ID` → DC_SCENE，可能设计为间接关联课程)
        
    - 实际学生来自班级分组 (`TE_CLASSGROUP_INFO` 通过 `CLASS_CODE` 关联课程)
        
    - 马甲角色继承自场景分组中的配置 (`DC_SCENE_PUPPET`)
        

#### 示例流程：学生参与课程任务

1. 教师基于场景创建课程，自动生成阶段和分组。
    
2. 系统从班级 (`TE_CLASS_INFO`) 导入学生到课程，并分配至课程分组。
    
3. 学生在课程阶段中完成任务，提交至 `DC_COURSE_TASK_HISTORY`。
    
4. 教师下发场景中的试卷，学生答题结果存入 `DC_COURSE_QUESTION_HISTORY`。
    
5. 分组讨论内容实时记录在 `DC_COURSE_CHAT_HISTORY` 中。