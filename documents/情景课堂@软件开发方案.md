---
date: 2025年6月12日
aliases:
  - 
---
# 课堂情景软件开发方案

## 一、技术选型

1. __后端__：采用 JavaWeb 开发，基于 Spring Boot 框架构建，具备快速开发、便捷配置和高效运行的优势，能够轻松整合各种后端组件和技术，如使用 MyBatis 进行数据库持久层操作，连接 达梦数据库存储数据，确保数据的稳定存储和高效读写。
2. __教师端前端__：运用 Vue\.js 框架，结合 Element UI 组件库进行页面搭建，实现简洁、高效的用户界面交互，通过 Axios 与后端进行数据交互，保证数据传输的稳定与快速，提升用户操作体验。
3. __学生端前端__：采用微信小程序开发，利用其原生 API 和框架特性，结合政务云开发能力，实现轻量化、便捷的学生端应用，满足学生随时随地参与课堂互动的需求，同时保证与教师端的数据实时同步和交互流畅性。

## 二、功能模块

### 教师端

1. __情景课堂创建__：教师输入课程名称、简介、预计时长等基本信息，创建情景课堂，系统自动生成唯一的课堂识别码，方便学生加入。同时，教师可设置课堂的开放时间和参与权限，结合智慧校园选择现有系统的班级人员加入等。
2. __学员分组管理__：支持多种分组方式，1、默认使用智慧校园已分组信息。2、教师可以手动输入学生名单进行逐个分组，3、可以设置每组人数智能分组。分组完成后，教师能够对各小组进行编辑，如调整成员、重命名小组、设置小组组长等，方便对不同小组进行差异化管理和任务分配。
3. __角色马甲设置__：教师可以为每个学生或小组定制独特的角色马甲，如市民、公安、消防、医疗、民政等部门人员，并为角色赋予相应的背景信息和任务说明，增强情景模拟的真实感和趣味性，让学生更好地融入课堂情境。每个角色具备特定权限和操作界面，市民角色可发布求助信息等，部门角色可进行相应应急处置操作，如公安发布交通管制信息、调配警力等，实现多角色互动模拟。
4. __聊天监控__：教师端以列表或面板展示各小组的聊天窗口，实时更新聊天内容，可查看学生的发言时间、发言内容。同时，教师能够对不当言论进行及时提醒和管理，但不直接参与学生的讨论，保障学生自主交流的氛围。
5. __任务下发__：教师编写任务标题、详细描述、任务要求和截止时间等信息，在任务分配方面，除了按组别和全体人员下发外，还可以针对特定学生个体进行单独任务布置，满足个性化教学需求。学生端收到任务后会有弹窗和消息提醒，并在任务列表中显示任务详情。
6. __问卷管理__：教师创建问卷时，可设置问卷标题、说明和截止时间，灵活添加多种类型的问题，如单选题、多选题、评分题等。在问卷发放时，可以按组别、个人或全体人员下发。问卷回收后，系统自动统计答题情况，生成详细的统计报告，包括各题的答题正确率、得分分布、学生答案详情等，以图表（柱状图、饼图、折线图等）和表格形式呈现，方便教师直观了解学生对知识点的掌握程度和反馈情况，为后续教学提供参考依据。
7. __情景阶段管理__：教师根据教学大纲和实际课堂进度，创建多个情景阶段，如 “情景导入”“知识讲解”“小组讨论”“任务实践”“成果展示”“总结评价” 等，并为每个阶段设置详细的时间限制、任务目标和过渡说明。在课堂进行过程中，教师可以在教师端控制面板上灵活控制每个阶段的开始、暂停、继续和结束操作，同时可以一键切换到任意阶段，方便根据学生的课堂表现和实际情况进行灵活调整，引导学生有序地参与课堂活动，确保情景教学的流畅性和完整性。

### 学生端（微信小程序）

1. __课堂加入__：结合智慧校园绑定信息直接进入，或者学生在微信小程序中输入教师提供的课堂识别码，验证身份后即可加入相应的情景课堂，进入后自动显示所在小组名称、成员列表以及情景课堂基本信息，如课程名称、当前阶段等。
2. __分组讨论__：学生在小组讨论区以聊天文字形式展示发言内容，方便学生之间进行深入的交流和讨论，共同完成小组任务和情景模拟活动。
3. __群聊功能__：学生可以在班级群聊界面与全班同学进行交流，分享学习心得、提问求助等。如果设置了马甲聊天内容均以马甲的身份进行。 
4. __问卷填写__：学生在收到问卷后，点击进入问卷答题界面，根据题目要求进行答题操作。答题过程中，系统自动保存学生的答题进度，学生可以随时返回修改答案（在截止时间前）。
