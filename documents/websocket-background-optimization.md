# WebSocket 后台前台切换优化

## 概述

优化了小程序在后台前台切换时的WebSocket连接和消息加载体验，确保用户在从后台切回前台时能够及时重新连接并获取最新消息。

## 优化内容

### 1. 应用级生命周期管理（App.vue）

- **onShow**: 记录应用进入前台的时间，标记前台状态
- **onHide**: 记录应用进入后台的时间，标记后台状态
- 通过 `getApp().globalData` 全局共享状态信息

### 2. 页面级生命周期管理（index.vue）

#### onShow 优化
- **智能检测**: 通过多个条件判断是否需要重连WebSocket
  - WebSocket连接状态
  - 群组信息可用性
  - 后台时间长度（>10秒）
  - 页面不可见时长（>30秒）

- **自动重连**: 当检测到需要重连时自动执行
  - 重新建立WebSocket连接
  - 重新加载当前群组的历史消息
  - 发送JOIN消息通知后端用户重新进入

- **用户反馈**: 提供合适的加载提示
  - "正在恢复连接..." - 重连时显示
  - "刷新消息中..." - 刷新消息时显示

#### onHide 新增
- 记录页面隐藏时间
- 标记页面可见性状态

### 3. 智能检测机制

```javascript
const shouldReconnectWebSocket = () => {
    // 1. 检查WebSocket连接状态
    // 2. 验证群组信息可用性  
    // 3. 判断是否从后台恢复
    // 4. 检查页面不可见时长
    // 5. 综合判断是否需要重连
}
```

## 使用场景

### 场景1: 短时间切换
- 用户快速切换应用（<10秒）
- 不触发重连，保持现有连接

### 场景2: 长时间后台
- 用户将应用放置后台较长时间（>10秒）
- 自动检测并重连WebSocket
- 重新加载消息确保数据最新

### 场景3: 网络变化
- 在后台期间网络环境发生变化
- 智能检测连接状态并重新建立连接

## 优化效果

1. **连接可靠性**: 确保WebSocket连接在后台前台切换时的稳定性
2. **消息完整性**: 防止在后台期间遗漏重要消息
3. **用户体验**: 提供清晰的状态反馈，减少用户困惑
4. **性能优化**: 避免不必要的重连，节省资源

## 技术实现

- 利用UniApp的应用和页面生命周期
- 全局状态管理记录应用状态变化
- 智能算法判断最佳重连时机
- 异步处理确保用户界面响应流畅 