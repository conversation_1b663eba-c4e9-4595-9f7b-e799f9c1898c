# WebSocket 缓存管理改进

## 概述

本次改进主要解决以下问题：
1. 服务重启时需要清理 WebSocket 在线用户缓存
2. 群组用户列表中头像字段为空的问题
3. 提供更好的缓存管理策略

## 主要改进

### 1. 启动时缓存清理

#### 新增组件
- **WebSocketCacheInitializer**: 应用启动时自动清理缓存
- **WebSocketCacheStrategy**: 提供多种缓存管理策略

#### 功能特性
- ✅ 应用启动时自动清理所有 WebSocket 相关缓存
- ✅ 支持智能清理策略（只清理过期数据）
- ✅ 优雅关闭时的缓存处理
- ✅ 可配置的清理策略

#### 配置项
```yaml
websocket:
  cache:
    # 是否在启动时清理缓存（单机部署建议为true，集群部署建议为false）
    clear-on-startup: true
    # 缓存过期时间（秒）
    expire-seconds: 300
    # 是否启用优雅关闭
    graceful-shutdown: true
```

### 2. 头像字段修复

#### 问题原因
之前的代码中，用户头像字段传递时优先级设置不正确，导致大部分情况下传递空字符串。

#### 解决方案
1. **修改 SQL 查询**: 在 `selectStudentCourseInfo` 中添加用户的 `avatar` 字段
2. **更新 DTO**: 在 `StudentCourseInfoDTO` 中添加 `avatar` 字段
3. **修复头像优先级逻辑**:
   - 优先使用 `puppetIcon`（马甲头像）
   - 如果没有马甲头像，则使用用户的 `avatar`（真实头像）
   - 公共聊天室统一使用用户真实头像
4. **修复无课程群组场景**: 即使用户没有加入任何课程群组，加入公共聊天室时也会查询并使用用户的真实头像

#### 代码改进
```java
// 优先使用puppetIcon作为头像，没有的话使用用户的avatar
String displayAvatar = "";
if (courseInfo.getPuppetIcon() != null && !courseInfo.getPuppetIcon().trim().isEmpty()) {
    displayAvatar = courseInfo.getPuppetIcon();
} else if (courseInfo.getAvatar() != null && !courseInfo.getAvatar().trim().isEmpty()) {
    displayAvatar = courseInfo.getAvatar();
}
```

**无课程群组场景的改进**:
```java
// 即使没有课程群组，也查询用户基本信息获取头像
List<StudentCourseInfoDTO> userBasicInfo = stSysUserMapper.selectStudentCourseInfo(null, userIdLong);

String userName = userIdStr;
String nickName = userIdStr;
String avatar = "";

// 如果查询到用户基本信息，则使用真实信息
if (userBasicInfo != null && !userBasicInfo.isEmpty()) {
    StudentCourseInfoDTO userInfo = userBasicInfo.get(0);
    userName = userInfo.getUserName() != null ? userInfo.getUserName() : userIdStr;
    nickName = userInfo.getNickName() != null ? userInfo.getNickName() : userIdStr;
    avatar = userInfo.getAvatar() != null ? userInfo.getAvatar() : "";
}
```

### 3. 管理员接口

新增管理员接口用于手动管理缓存：

#### 缓存统计
```http
GET /student/online/cache/stats
```
返回当前 WebSocket 缓存的统计信息。

#### 手动清理缓存
```http
POST /student/online/cache/clear
```
紧急情况下手动清理所有 WebSocket 缓存。

## 部署建议

### 单机部署
```yaml
websocket:
  cache:
    clear-on-startup: true  # 启动时强制清理
    expire-seconds: 300
    graceful-shutdown: true
```

### 集群部署
```yaml
websocket:
  cache:
    clear-on-startup: false  # 使用智能清理策略
    expire-seconds: 300
    graceful-shutdown: true
```

## 最佳实践

### 1. 监控建议
- 定期检查缓存统计信息 `/student/online/cache/stats`
- 关注应用日志中的缓存清理信息
- 监控 Redis 内存使用情况

### 2. 故障恢复
如果遇到缓存异常：
1. 检查 Redis 连接状态
2. 调用手动清理接口清理缓存
3. 重启应用程序

### 3. 性能优化
- 缓存过期时间可根据业务需求调整
- 高频场景下可考虑增加缓存预热机制
- 定期清理无效的群组缓存

## 升级说明

### 数据库无需变更
本次改进只涉及代码逻辑和配置，无需修改数据库结构。

### 配置更新
在 `application.yml` 中添加 WebSocket 缓存配置项（可选，有默认值）。

### 向后兼容
- 保持现有 API 接口不变
- 新增的管理员接口为额外功能
- 现有缓存逻辑保持兼容

## 测试验证

### 1. 启动清理验证
1. 启动应用前在 Redis 中手动添加一些测试数据
2. 启动应用，检查日志中的清理记录
3. 验证缓存确实被清理

### 2. 头像显示验证
1. 登录有马甲头像的用户，检查群组成员列表
2. 登录无马甲头像的用户，检查是否显示真实头像
3. 检查公共聊天室中的头像显示

### 3. 缓存策略验证
1. 测试智能清理策略
2. 测试优雅关闭功能
3. 验证管理员接口功能 